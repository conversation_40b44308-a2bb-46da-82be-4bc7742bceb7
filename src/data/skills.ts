export interface Skill {
  icon: string;
  image: string;
  title: string;
  subtitle: string;
  description: string;
  category: 'frontend' | 'backend' | 'tools' | 'design';
}

export const skills: Skill[] = [
  {
    icon: "IconJS",
    image: "/icons/javascript.svg",
    title: "JavaScript",
    subtitle: "ES6+, TypeScript",
    description: "I bring websites to life with JavaScript, adding interactive features and optimizing performance with clean and efficient code.",
    category: "frontend"
  },
  {
    icon: "IconReact",
    image: "/icons/react.svg",
    title: "React JS",
    subtitle: "Next.js, Hooks, Context",
    description: "Using React, I craft scalable user interfaces with components, state management, and virtual DOM manipulation for seamless web applications.",
    category: "frontend"
  },
  {
    icon: "IconTypeScript",
    image: "/icons/typescript.svg",
    title: "TypeScript",
    subtitle: "Type Safety, Interfaces",
    description: "I leverage TypeScript to build robust, maintainable applications with strong typing and enhanced developer experience.",
    category: "frontend"
  },
  {
    icon: "IconCSS",
    image: "/icons/css.svg",
    title: "CSS",
    subtitle: "Animations, Sass, Tailwind",
    description: "I prioritize pixel perfection, paying close attention to details for visually polished and precisely aligned designs, ensuring a seamless user experience.",
    category: "frontend"
  },
  {
    icon: "IconHTML",
    image: "/icons/html.svg",
    title: "HTML",
    subtitle: "Semantic Markup, A11y",
    description: "Beyond markup, HTML is crucial for an inclusive web. I structure content carefully for universal accessibility.",
    category: "frontend"
  },
  {
    icon: "IconNode",
    image: "/icons/nodejs.svg",
    title: "Node.js",
    subtitle: "Express, APIs, Microservices",
    description: "I use Node.js to write server-side applications with access to the operating system, file system, and everything else required to build fully-functional applications.",
    category: "backend"
  },
  {
    icon: "IconMongoDB",
    image: "/icons/mongodb.svg",
    title: "MongoDB",
    subtitle: "NoSQL, Aggregation",
    description: "I use MongoDB for developing scalable applications with evolving data schemas and complex data relationships.",
    category: "backend"
  },
  {
    icon: "IconDjango",
    image: "/icons/django.svg",
    title: "Django",
    subtitle: "Python, REST APIs",
    description: "I use Django with Python to build secure, scalable web applications quickly, leveraging its powerful features and built-in admin interface.",
    category: "backend"
  },
  {
    icon: "IconFigma",
    image: "/icons/figma.svg",
    title: "Design",
    subtitle: "Figma, UI/UX",
    description: "While not a full-time UI designer, I have an eye for aesthetics, creating visually appealing and user-friendly interfaces.",
    category: "design"
  },
  {
    icon: "IconWebpack",
    image: "/icons/webpack.svg",
    title: "Build Tools",
    subtitle: "Webpack, Vite, Parcel",
    description: "I use modern build tools to streamline workflows, automating development processes for maximum efficiency.",
    category: "tools"
  },
  {
    icon: "IconAPI",
    image: "/icons/api.svg",
    title: "REST APIs",
    subtitle: "Axios, Fetch, GraphQL",
    description: "I integrate data and functionality seamlessly with REST APIs and GraphQL, creating dynamic web applications for enhanced performance.",
    category: "backend"
  },
  {
    icon: "IconGSAP",
    image: "/icons/gsap.svg",
    title: "Animations",
    subtitle: "GSAP, Framer Motion",
    description: "I use GSAP and Framer Motion for captivating animations that bring web pages to life, enhancing the overall user experience.",
    category: "frontend"
  }
];
