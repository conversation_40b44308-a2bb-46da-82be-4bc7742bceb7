export interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  location: string;
  description: string;
  technologies: string[];
  achievements: string[];
  current: boolean;
}

export const experiences: Experience[] = [
  {
    id: "freelance-2024",
    company: "Freelance Developer",
    position: "FullStack Developer",
    duration: "2022 - Present",
    location: "Hanoi, Vietnam",
    description: "Working as a freelance fullstack developer, creating modern web applications for clients worldwide. Specializing in React, Node.js, and modern web technologies.",
    technologies: ["React", "TypeScript", "Node.js", "MongoDB", "PostgreSQL", "Docker", "AWS"],
    achievements: [
      "Delivered 15+ successful projects for international clients",
      "Improved client website performance by 40% on average",
      "Built scalable applications serving 10k+ users",
      "Maintained 98% client satisfaction rate"
    ],
    current: true
  },
  {
    id: "tech-company-2021",
    company: "Tech Solutions Inc.",
    position: "Senior Frontend Developer",
    duration: "2020 - 2022",
    location: "Ho Chi Minh City, Vietnam",
    description: "Led frontend development for enterprise web applications, mentored junior developers, and implemented modern development practices.",
    technologies: ["React", "Vue.js", "TypeScript", "SASS", "Webpack", "Jest"],
    achievements: [
      "Led a team of 5 frontend developers",
      "Reduced bundle size by 60% through optimization",
      "Implemented automated testing increasing code coverage to 85%",
      "Mentored 3 junior developers to mid-level positions"
    ],
    current: false
  },
  {
    id: "startup-2019",
    company: "InnovateTech Startup",
    position: "Frontend Developer",
    duration: "2018 - 2020",
    location: "Hanoi, Vietnam",
    description: "Developed user interfaces for a fast-growing fintech startup, working closely with designers and backend developers to create seamless user experiences.",
    technologies: ["React", "JavaScript", "CSS3", "Redux", "Node.js", "MongoDB"],
    achievements: [
      "Built the main product interface from scratch",
      "Implemented responsive design supporting 5+ device types",
      "Contributed to 300% user growth over 2 years",
      "Optimized app performance resulting in 50% faster load times"
    ],
    current: false
  }
];

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  duration: string;
  location: string;
  description?: string;
  achievements?: string[];
}

export const education: Education[] = [
  {
    id: "university-cs",
    institution: "Hanoi University of Science and Technology",
    degree: "Bachelor of Science",
    field: "Computer Science",
    duration: "2014 - 2018",
    location: "Hanoi, Vietnam",
    description: "Focused on software engineering, algorithms, and web development. Graduated with honors.",
    achievements: [
      "Graduated Magna Cum Laude (GPA: 3.8/4.0)",
      "President of Computer Science Student Association",
      "Winner of University Programming Contest 2017",
      "Published research paper on web performance optimization"
    ]
  }
];
