export interface Project {
  id: string;
  title: string;
  image: string;
  description: string;
  url?: string;
  github?: string;
  date: string;
  service: string;
  status: boolean;
  technologies: string[];
  category: 'web' | 'mobile' | 'fullstack' | 'frontend' | 'backend';
  featured: boolean;
  color: {
    h: string;
    s: string;
    l: string;
  };
}

export const projects: Project[] = [
  {
    id: "trust-framer",
    title: "Trust Framer Portfolio",
    image: "/projects/trust-framer.jpg",
    description: "A modern, animated portfolio website built with React, TypeScript, and Framer Motion. Features smooth animations, responsive design, and optimized performance.",
    url: "",
    github: "https://github.com/techEdge3030/trust-framer",
    date: "2024",
    service: "FullStack Development",
    status: true,
    technologies: ["React", "TypeScript", "Framer Motion", "Tailwind CSS", "Vite"],
    category: "frontend",
    featured: true,
    color: {
      h: "240",
      s: "100%",
      l: "50%"
    }
  },
  {
    id: "uae-yearof",
    title: "UAE Year of Sustainability",
    image: "/projects/uae-yearof.jpg",
    description: "Welcome to the Year of Sustainability. Sustainability is about protecting our land, resources and one another today, for a better tomorrow.",
    url: "https://uaeyearof.ae/",
    date: "2023",
    service: "FullStack Development",
    status: false,
    technologies: ["React", "Node.js", "MongoDB", "Express"],
    category: "fullstack",
    featured: true,
    color: {
      h: "30",
      s: "100%",
      l: "90%"
    }
  },
  {
    id: "rqitects",
    title: "R Qitects",
    image: "/projects/rqitects.jpg",
    description: "Premier design studio and architecture company in the UAE. Enhance your projects with our top architects, leading the way in innovative and timeless design solutions.",
    url: "https://rqitects.com/",
    date: "2022",
    service: "Frontend Development",
    status: false,
    technologies: ["React", "GSAP", "CSS3", "JavaScript"],
    category: "frontend",
    featured: true,
    color: {
      h: "0",
      s: "0%",
      l: "0%"
    }
  },
  {
    id: "fintx",
    title: "FINTX",
    image: "/projects/fintx.jpg",
    description: "FINTX is the fintech arm of the Emirates Post Group, with the vision of building a fintech ecosystem for the UAE & beyond.",
    url: "https://fintx.ae/",
    date: "2023",
    service: "FullStack Development",
    status: false,
    technologies: ["React", "Node.js", "PostgreSQL", "Docker"],
    category: "fullstack",
    featured: false,
    color: {
      h: "70",
      s: "100%",
      l: "50%"
    }
  },
  {
    id: "g42",
    title: "G42 AI",
    image: "/projects/g42.jpg",
    description: "G42 is a technology group that invents visionary artificial intelligence for a better everyday. Born in Abu Dhabi and operating across the world.",
    url: "https://g42.ai/",
    date: "2022",
    service: "Frontend Development",
    status: false,
    technologies: ["React", "TypeScript", "Three.js", "WebGL"],
    category: "frontend",
    featured: false,
    color: {
      h: "0",
      s: "0%",
      l: "100%"
    }
  },
  {
    id: "ferronato",
    title: "Ferronato Swiss",
    image: "/projects/ferronato.jpg",
    description: "In a world of data theft and device tracking, FERRONATO is the first luxury accessories range to combine privacy technology with handcrafted Italian design.",
    url: "https://www.ferronato-switzerland.com/en/the-luxury-of-privacy/technology",
    date: "2022",
    service: "Frontend Development",
    status: false,
    technologies: ["React", "GSAP", "CSS3", "JavaScript"],
    category: "frontend",
    featured: false,
    color: {
      h: "207",
      s: "18%",
      l: "24%"
    }
  }
];
