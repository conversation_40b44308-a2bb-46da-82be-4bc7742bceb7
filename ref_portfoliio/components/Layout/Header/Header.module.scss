@import '/assets/media-queries.scss';

.header{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 2;

  /*&.menuOpen{

  }*/
}
.inner{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--x-spacing);
  height: var(--header-height);
}
.logo{
  display: inline-block;
}

.menuToggle{
  --size: 5rem;
  --lineHeight: 0.2rem;
  --lineWidth: 40%;
  position: relative;
  background: none;
  border: 0;
  padding: 0;
  width: var(--size);
  height: var(--size);
  cursor: pointer;

  @include respond-above(xs) {
    display: none;
  }

  span{
    display: block;
    width: var(--lineWidth);
    height: var(--lineHeight);
    background: hsl(var(--color-white));

    position: absolute;
    transition: all 0.3s ease-in-out;
    &:nth-child(1){
      transform-origin: right bottom;
      top: calc(50% - var(--lineHeight) * 3);
      left: 10%;
    }
    &:nth-child(2){
      transform-origin: left bottom;
      top: calc(50% - var(--lineHeight) * 3);
      left: 50%;
    }
    &:nth-child(3){
      transform-origin: right top;
      top: calc(50% + var(--lineHeight) * 3);
      left: 10%;
    }
    &:nth-child(4){
      transform-origin: left bottom;
      top: calc(50% + var(--lineHeight) * 3);
      left: 50%;
    }
  }


  &:hover{
    span{
      &:nth-child(1){
        transform: rotateZ(45deg);
        top: 50%;
      }
      &:nth-child(2){
        transform: rotateZ(-45deg);
        top: 50%;
      }
      &:nth-child(3){
        transform: rotateZ(-45deg);
        top: 50%;
      }
      &:nth-child(4){
        transform: rotateZ(45deg);
        top: 50%;
      }
    }
  }
}

.info{
  font-size: var(--fs-xs);
  color: hsl(var(--color-white));
}
.openToWork{
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: var(--fs-ms);
  color: hsla(var(--color-white), .9);
  line-height: 1;
  border: 1px solid hsla(var(--color-white), .3);
  padding: 0.8rem 1rem;
  border-radius: 100rem;
  text-transform: uppercase;
  @include respond-below(xs) {
    display: none;
  }
  span{
    position: relative;
    box-sizing: content-box;
    width: 0.8rem;
    height: 0.8rem;
    border-radius: 50%;
    background: hsl(var(--color-success));
    &:after{
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      animation: pulsate 2s infinite;
      border-radius: 50%;
      -webkit-box-shadow: 0 0 0 0 hsl(var(--color-success));
      box-shadow: 0 0 0 0 hsl(var(--color-success));
    }
  }
  @keyframes pulsate {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 hsla(var(--color-success), .1);
    }
    50% {
      transform: scale(1);
      box-shadow: 0 0 0 0.8rem hsla(var(--color-success), .2);
    }
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 hsla(var(--color-success), .1);
    }
  }
}

.location{
  margin-left: auto;
  margin-right: 3rem;
  font-size: var(--fs-ms);
  color: hsla(var(--color-white), .9);
  text-transform: uppercase;
  @include respond-below(xs) {
    display: none;
  }
}