@import '/assets/media-queries.scss';

.container{
  position: fixed;
  bottom: calc(var(--x-spacing) / 3);
  left: 50%;
  transform: translateX(-50%);
  transition: transform .3s ease-in-out;

  display: flex;
  justify-content: center;

  @include respond-below(xs) {
    transform: translateX(100%);
    inset: 0;
    width: 100%;
    height: 100%;
  }
  &.menuOpen{
    transform: translateX(0);
  }
}

.navigation{
  position: relative;
  z-index: 1;
  border-radius: 100vw;
  backdrop-filter: blur(.4rem);
  background-color: hsla(var(--color-black), .8);
  border: .05rem solid hsla(var(--color-white), .16);
  box-shadow: .01rem .01rem .07rem hsla(var(--color-black), .16);
  @include respond-below(xs) {
    background-color: hsla(var(--color-black), .95);
    border-radius: 0;
    width: 100%;
    padding-top: var(--header-height);
  }
  ul{
    display: flex;
    padding: 0 1.5rem;
    height: 6rem;
    align-items: center;
    @include respond-below(xs) {
      display: block;
    }
    li{
      a, button{
        display: block;
        padding: 1rem 1.5rem;
        font-size: var(--fs-xxs);
        color: hsl(var(--color-white));
        text-decoration: none;
        transition: all .3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 100vw;
        border: 0;
        background: transparent;
        @include respond-below(xs) {
          --fs-xxs: 2rem;
          display: block;
          width: 100%;
          padding: 2rem 3rem;
          text-align: left;
        }
        &:hover{
          cursor: pointer;
          color: hsl(var(--color-black));
        }
      }
    }
  }
}

.bg{
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  background: hsl(var(--color-primary));
  pointer-events: none;
  border-radius: 100vw;
  height: 4.6rem;
}