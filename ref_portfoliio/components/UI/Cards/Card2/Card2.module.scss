.card{
  position: relative;
  padding: 3rem;
  z-index: 0;
  &:after{
    content: '';
    position: absolute;
    inset: 0;
    width: 100%;
    z-index: -1;
    border-radius: 2rem;
    background-color: hsl(var(--color-primary));
    transform: scale(0.9);
    opacity: 0;
    transition: all .3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }
  a{
    text-decoration: none;
  }
  .title{
    margin-bottom: 1.5rem;
    font-size: var(--fs-md);
    color: hsl(var(--color-black));
    font-weight: var(--fw-medium);
  }
  .description{
    margin-bottom: 3rem;
    font-size: var(--fs-sm);
    color: hsl(var(--color-body));
  }
  .date{
    display: inline-block;
    margin-bottom: 1.5rem;
    font-size: var(--fs-xs);
    color: hsl(var(--color-body));
  }
  .cta{
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    font-size: var(--fs-xs);
    color: hsl(var(--color-black));
    svg{
      width: 3rem;
      height: 3rem;
      transform: scale(0) rotate(-180deg);
      transition: all .3s ease;
    }
  }

  &:hover{
    .date, .description {
      color: hsl(var(--color-black));
    }
.cta svg{
      transform: scale(1) rotate(0deg);
    }
    &:after{
      transform: scale(1);
      opacity: 1;
    }
  }
}
