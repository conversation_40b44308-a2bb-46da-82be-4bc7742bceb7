@import '/assets/media-queries.scss';
.nav{

}
.ul{
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6rem;
  padding-left: 0;
  @include respond-below(lg) {
    grid-template-columns: repeat(auto-fill, minmax(22rem, 1fr))
  }
  @include respond-below(sm) {
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(10rem, 1fr))
  }
  li{
    &:nth-child(1), &:nth-child(2){
      @include respond-above(lg) {
        grid-column: 2 / 3;
      }
    }
    a, button{
      display: block;
      padding: 1rem;
      text-decoration: none;
      border: 0;
      background: transparent;
      text-align: left;
      &:hover{
        cursor: pointer;
      }
    }
  }
}
.title{
  position: relative;
  overflow: hidden;
  display: block;
  margin-bottom: 1rem;
  color: hsl(var(--color-white));
  font-weight: var(--fw-medium);
  font-size: var(--fs-sm);
  @include respond-below(sm) {
    margin-bottom: 0;
  }

  span  {
    display: block;
    transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    a:hover &, button:hover & {
      transform: translateY(-100%);
    }
    &:after{
      content: attr(data-text);
      display: block;
      position: absolute;
      top: 100%;
      left: 0;
    }
  }

  a:hover &, button:hover & {
    color: hsl(var(--color-primary));
  }
}
.description{
  color: hsl(var(--color-body));
  font-weight: var(--fw-regular);
  font-size: var(--fs-xs);
  @include respond-below(sm) {
    display: none;
  }
}