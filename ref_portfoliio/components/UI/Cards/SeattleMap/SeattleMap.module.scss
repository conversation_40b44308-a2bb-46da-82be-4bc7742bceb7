.container{
  position: relative;
  width: 100%;
  height: fit-content;
  border: 4px solid hsla(var(--color-black), 0.2);
  border-radius: 2rem;
  overflow: hidden;
  font-size: 0;
  &:after{
    content: '';
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    box-shadow: inset 0 0 30px 0 hsla(var(--color-black), 0.15);
    pointer-events: none;
  }
}
.map{
  width: 100%;
  height: auto;
  /*rect{fill: #81CFE4;}
  path{fill: #B8F9D3;}*/
  rect{fill: #0b0b0e;}
  path{fill: #151515;}
}
.city{
  position: absolute;
  color: hsla(var(--color-white), 1);
  &.v1{
    font-size: 1.4rem;
    font-weight: var(--fw-medium);
  }
  &.v2{
    font-size: 1.2rem;
    color: hsla(var(--color-white), 1);
  }
  &.v3{
    font-size: 1.2rem;
    color: hsla(var(--color-white), 0.5);
  }
}

.pinWrapper{
  position: absolute;
  top: 50.5%;
  left: 50%;
  display: block;
  width: 1.8rem;
  height: 1.8rem;
}
.pin{
  display: block;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 0 0 hsla(var(--color-alert), 1);
  animation: pulsate 2s infinite;
  border: 2px solid white;

  background: hsla(var(--color-alert), 1);
  border-radius: 50%;
  transform: scale(1);
}
@keyframes pulsate {
  0% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 hsla(var(--color-alert), .7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 1.5rem hsla(var(--color-alert), 0);
  }

  100% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 hsla(var(--color-alert), 0);
  }
}
