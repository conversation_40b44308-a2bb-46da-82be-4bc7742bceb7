'use client';

import React, { useRef } from 'react';
import styles from './SeattleMap.module.scss';

import gsap from "gsap";
import { useGSAP } from "@gsap/react";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import {getRandomValue} from "@/utils/utils";

export default function SeattleMap({className}) {
    const container = useRef();

    useGSAP(() => {
        gsap.registerPlugin(ScrollTrigger);

        const yPercentValue = getRandomValue(20, 10);
        gsap.to(container.current, {
            yPercent: -yPercentValue,
            ease: 'Power2.out',
            scrollTrigger: {
                trigger: container.current,
                scrub: true,
            }
        });
        gsap.from(`.${styles.city}`, {
            autoAlpha: 0,
            scale: 0,
            delay: 0.8,
            stagger: 0.3,
            scrollTrigger: {
                trigger: container.current,
                start: 'top 80%',
                end: 'center center',
                scrub: true,
            }
        });
        gsap.from(`.${styles.pinWrapper}`, {
            autoAlpha: 0,
            scrollTrigger: {
                trigger: container.current,
                start: 'top 50%',
                end: 'bottom bottom',
                scrub: true,
            }
        });
    }, { scope: container });

    return (
        <div className={`${styles.container} ${className}`} ref={container}>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '20%',
                left: '75%',
            }}>Manroe</span>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '30%',
                left: '50%',
            }}>Edmonds</span>
            <span className={`${styles.city} ${styles.v2}`} style={{
                top: '45%',
                left: '61%',
            }}>Bellevue</span>
            <span className={`${styles.city} ${styles.v1}`} style={{
                top: '45%',
                left: '35%',
            }}>Seattle</span>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '45%',
                left: '2%',
            }}>Seabeck</span>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '58%',
                left: '58%',
            }}>Renton</span>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '60%',
                left: '1%',
            }}>Belfair</span>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '62%',
                left: '18%',
            }}>Vashon Island</span>
            <span className={`${styles.city} ${styles.v3}`} style={{
                top: '73%',
                left: '52%',
            }}>Federal Way</span>
            <span className={`${styles.city} ${styles.v2}`} style={{
                top: '82%',
                left: '36%',
            }}>Tacoma</span>

            <span className={styles.pinWrapper}>
                <span className={styles.pin}></span>
            </span>

            <svg className={styles.map} width="520" height="780" viewBox="0 0 520 780" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clipPath="url(#clip0_2112_7669)">
                    <rect width="520" height="780" fill="#D6E2F1"/>
                    <path d="M173.942 597.576C169.928 595.088 161.649 589.948 160.653 589.285L166.882 607.11L151.931 644.833L140.718 683.8L124.937 699.967L115.8 718.207L88.8052 748.883L72.1931 780.803H725.049V-16.7738H251.189V0.636909L273.615 32.9711L283.997 42.5055L294.38 53.2836L308.916 68.6216L315.561 64.4762L347.124 55.7708L341.309 64.4762L330.096 72.3525L322.621 82.3014V103.028L308.916 119.61L296.457 121.683L279.429 129.559L274.861 148.213L267.385 176.817V191.74L263.232 202.518L252.85 212.467L236.653 233.194L234.992 249.776V253.921L239.145 264.285L240.391 273.819L244.959 289.986L242.883 301.594L229.593 316.102L227.932 329.368L252.85 345.535L227.932 333.099L216.718 338.902L219.626 346.778L232.915 359.629H240.391L263.232 382.844L261.156 392.378L263.232 425.956C260.741 420.29 255.674 408.711 255.342 407.716C255.009 406.721 251.05 399.563 249.112 396.109L244.959 395.28L237.484 386.574L222.117 401.083L231.669 416.007L232.915 429.272L231.669 444.196L250.773 468.239L248.281 484.406C246.066 486.893 241.387 492.199 240.391 493.526C239.394 494.852 249.112 497.948 254.096 499.329L257.003 520.057L265.309 526.275L269.047 554.049L266.97 571.045L252.019 576.849L224.194 588.041L208.828 602.136L230.008 615.815L222.948 619.961L212.981 634.884C209.935 630.877 203.595 622.78 202.598 622.448C201.352 622.034 190.554 614.986 189.724 614.572C189.059 614.24 178.926 603.103 173.942 597.576Z" fill="#F7F8FC"/>
                    <path d="M384.916 345.535L375.779 344.291L377.441 351.338L386.993 360.458L385.747 365.018L373.703 379.942L374.533 401.498L386.993 406.058L388.654 413.105L392.807 416.421L399.867 408.131L394.883 404.4L385.747 392.378L394.883 374.553V367.091L392.807 357.971L384.916 345.535Z" fill="#D6E2F1"/>
                    <path d="M302.686 267.601H298.118L289.396 276.307V290.815L298.533 319.419L308.085 325.637L292.303 345.535L283.997 348.437L292.303 351.338L291.058 364.189L289.396 385.745V398.182L300.194 417.665H306.424L300.194 425.127V442.123L313.899 452.486H324.282L332.173 430.101L335.495 398.182L327.604 394.865L320.129 381.185L309.746 375.796V353.826L320.129 350.509L325.943 343.462L324.282 325.637L322.205 307.397L313.899 311.957L302.686 300.35L298.118 283.768L302.686 267.601Z" fill="#D6E2F1"/>
                    <path d="M311.823 389.476L308.5 387.404L303.517 391.134L306.009 399.011L314.315 409.789V418.08L308.5 431.759L317.222 438.392L324.697 430.101V411.447L328.02 400.669L317.222 393.622L315.976 387.404L311.823 389.476Z" fill="#F7F8FC"/>
                    <path d="M215.473 551.562C211.818 553.552 205.644 564.827 203.013 570.216L195.538 562.754L205.09 549.489L213.811 539.125L206.336 525.86L200.106 527.518L206.336 540.369L193.046 541.613L186.401 554.464C185.986 562.616 184.74 578.921 183.079 578.921H173.112L168.128 571.874L174.773 554.464C174.773 551.424 174.441 545.095 173.112 544.1C171.783 543.105 169.235 535.118 168.128 531.249L176.434 514.668L174.773 493.526L181.002 479.846L190.554 470.312L193.046 448.341L203.013 453.316L200.106 466.995L213.811 479.846L210.074 490.624L211.735 514.668V524.617L243.298 537.882C235.545 541.613 219.127 549.572 215.473 551.562Z" fill="#F7F8FC"/>
                    <path d="M142.794 637.842L86.3132 615.457L105.417 567.785L110.401 544.156L84.6521 559.909L51.0126 615.457L73.439 658.984L47.6902 700.023L15.7119 646.962L32.7393 605.508C33.0162 591.414 33.4038 562.562 32.7393 559.909C32.0748 557.256 25.2638 558.803 21.9414 559.909L3.66798 596.388L-30.3867 620.017L-36.2011 676.395L-104.726 748.525L-36.2011 689.66L-74.8242 768.008L-36.2011 718.678V762.619L-22.0807 768.008V724.896L21.9414 695.049L65.1329 748.525L58.9034 793.71L-173.251 782.517V-18.7903H-86.8679V36.3436L-60.7038 76.9687L-37.4469 27.6383L-5.46857 96.8666L11.5587 93.1357L26.925 69.5069L8.23631 44.6345L-0.0696259 10.2276L42.7066 36.3436L66.3787 62.4597L74.6849 93.1357L97.1111 127.128L124.521 176.044L92.9582 201.331L66.3787 234.494L58.0727 268.487L52.2585 315.33L23.6026 319.06L42.7066 250.661L26.925 207.134L3.66798 234.494L8.23631 268.487L-5.46857 297.919L-32.0481 349.737L-74.8242 405.285C-95.3125 441.765 -136.787 514.724 -138.781 514.724C-141.273 514.724 -143.765 555.763 -142.934 559.909C-142.269 563.225 -133.52 573.45 -129.229 578.148L-115.109 567.785H-83.5456L-36.2011 544.156L8.23631 514.724L3.66798 501.458L-57.3815 535.451L-78.562 555.763C-83.4072 554.658 -93.8451 551.701 -96.8353 548.716C-100.573 544.985 -122.169 538.767 -122.169 535.451C-122.169 532.134 -102.234 492.753 -96.8353 486.95C-92.5162 482.307 -80.3616 453.234 -74.8242 439.277L-32.0481 376.682L11.5587 353.882L58.0727 336.886L66.3787 283.41C76.6229 269.316 97.1111 240.464 97.1111 237.81C97.1111 235.157 123.691 210.451 136.98 198.429C140.579 208.793 148.359 228.773 150.685 225.789C153.011 222.804 154.423 217.913 154.838 215.84L124.521 147.855L134.073 137.906L168.543 156.146L175.188 179.775L180.172 215.84L191.385 225.789L184.74 245.687L196.784 268.487L191.385 278.021L157.745 268.487L147.778 297.919L134.073 312.428L100.434 283.41L121.614 312.428L124.521 336.886L134.073 357.198V395.336L84.6521 433.474L97.1111 435.961L124.521 426.012L154.838 395.336L161.068 435.961L184.74 442.179L175.188 468.295L157.745 526.745V548.716L136.98 578.148L157.745 615.457L142.794 637.842Z" fill="#F7F8FC"/>
                    <path d="M63.1647 717.076L77.7003 693.033L82.2686 688.058L97.6348 713.76L82.2686 732.414L63.1647 717.076Z" fill="#F7F8FC"/>
                </g>
                <defs>
                    <clipPath id="clip0_2112_7669">
                        <rect width="520" height="780" fill="white"/>
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
}