.element{
  --elementSize: 5.6rem;
  --figureSize: 4rem;

  width: var(--elementSize);
  height: var(--elementSize);
  border-radius: 100rem 100rem 100rem 0;
  background-color: hsl(var(--color-white));
  overflow: hidden;

  position: absolute;
  bottom: 5%;
  left: 5%;

  transition: all .2s linear;

  &:after{
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 6rem;
    background: linear-gradient(to right, transparent 0%, hsl(var(--color-white)) 50%);
    visibility: hidden;
  }

  &:hover{
    width: 32rem;
    height: 8rem;
    &:after{
      visibility: visible;
    }
  }
}

.figure{
  position: absolute;
  left: calc((var(--elementSize) - var(--figureSize)) / 2);
  top: 50%;
  transform: translateY(-50%);

  width: var(--figureSize);
  height: var(--figureSize);
  overflow: hidden;
  border-radius: 50%;
  img{
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.detail{
  position: absolute;
  left: calc( ((var(--elementSize) - var(--figureSize)) / 2) + var(--elementSize));
  top: 50%;
  transform: translateY(-50%);
  width: max-content;
  font-size: var(--fs-xs);
}
.date{
  color: hsl(var(--color-body));
}
.description{
  color: hsl(var(--color-black));
}