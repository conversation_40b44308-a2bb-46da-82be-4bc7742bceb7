export function IconA11Y() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M39.9111 7.5C22.0107 7.5 7.5 22.0107 7.5 39.9111C7.5 57.8115 22.0107 72.3223 39.9111 72.3223C57.8115 72.3223 72.3223 57.8115 72.3223 39.9111C72.3223 22.0107 57.8115 7.5 39.9111 7.5ZM39.4224 13.9365C41.7456 13.9365 43.6245 15.8154 43.6245 18.1196C43.6245 20.4429 41.7456 22.3218 39.4224 22.3218C37.1181 22.3218 35.2393 20.4429 35.2393 18.1196C35.2329 15.8154 37.1118 13.9365 39.4224 13.9365ZM57.583 27.4761L45.1162 29.0503L45.1225 41.5298L51.1655 61.6962C51.4829 62.9721 50.7212 64.2418 49.458 64.5591C48.2011 64.8765 46.9126 64.1782 46.5952 62.9087L40.4062 44.5512H38.502L32.8081 63.2895C32.332 64.5146 30.9672 65.0732 29.7485 64.5971C28.5361 64.1275 27.7998 62.75 28.2759 61.5249L33.5064 41.7329V29.0503L22.0171 27.4887C20.8364 27.3935 20.0366 26.3589 20.1319 25.1782C20.2207 23.9912 21.2935 23.1089 22.4677 23.2041L36.4263 24.4038H42.5454L57.4052 23.1787C58.5859 23.1342 59.5889 24.0484 59.627 25.2417C59.6777 26.416 58.7636 27.4252 57.583 27.4761Z" fill="#20c6ae"/>
        </svg>
    );
}

export function IconCSS() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2138_13175)">
                <path d="M68.1227 44.3196C65.3957 44.3347 63.0344 44.9842 61.054 45.9508C60.3227 44.5159 59.5914 43.2623 59.4696 42.3258C59.3325 41.2383 59.1649 40.5737 59.3325 39.2748C59.5 37.9758 60.2618 36.1331 60.2618 35.9821C60.2465 35.8461 60.0942 35.1816 58.525 35.1665C56.9559 35.1514 55.6 35.4685 55.4477 35.8764C55.2954 36.2842 54.9907 37.2206 54.7926 38.1873C54.5184 39.6071 51.6543 44.6368 50.0243 47.28C49.4911 46.2529 49.034 45.3467 48.9426 44.6217C48.8055 43.5342 48.6379 42.8696 48.8055 41.5706C48.9731 40.2717 49.7348 38.429 49.7348 38.2779C49.7196 38.142 49.5672 37.4774 47.9981 37.4623C46.4289 37.4472 45.0731 37.7644 44.9207 38.1722C44.7684 38.58 44.6008 39.5467 44.2657 40.4831C43.9457 41.4196 40.1372 49.8175 39.1469 52.0076C38.6442 53.1253 38.2024 54.0165 37.8825 54.6206C37.5625 55.2248 37.8672 54.6659 37.8368 54.7264C37.5625 55.2399 37.4102 55.5269 37.4102 55.5269V55.542C37.1969 55.9196 36.9684 56.2821 36.8618 56.2821C36.7856 56.2821 36.6332 55.2701 36.8922 53.8805C37.4559 50.9654 38.827 46.4191 38.8118 46.2529C38.8118 46.1774 39.0707 45.3769 37.9282 44.9691C36.8161 44.5612 36.42 45.2409 36.3286 45.2409C36.2372 45.2409 36.161 45.4826 36.161 45.4826C36.161 45.4826 37.395 40.3623 33.7997 40.3623C31.545 40.3623 28.4372 42.7941 26.8985 45.0144C25.9235 45.543 23.8516 46.6607 21.6579 47.854C20.82 48.3071 19.9516 48.7904 19.1289 49.2284L18.9614 49.0472C14.6043 44.4404 6.54536 41.1779 6.88051 34.9852C7.00239 32.7347 7.79457 26.7987 22.3586 19.6092C34.2872 13.7185 43.8391 15.3347 45.4997 18.9295C47.861 24.0649 40.3961 33.6107 27.9954 34.9852C23.2727 35.5139 20.7895 33.7014 20.1649 33.0217C19.5098 32.3118 19.4184 32.2816 19.1747 32.4175C18.7786 32.629 19.0223 33.2633 19.1747 33.6409C19.5403 34.5925 21.0637 36.2842 23.6536 37.13C25.9235 37.8701 31.4688 38.2779 38.1719 35.7102C45.6825 32.8253 51.5477 24.8201 49.8262 18.129C48.0743 11.317 36.6789 9.08156 25.9082 12.8727C19.4946 15.1383 12.5477 18.6727 7.55082 23.3097C1.60942 28.8076 0.664886 33.6107 1.06098 35.6045C2.44731 42.7185 12.3344 47.3555 16.2954 50.7842C16.0973 50.8899 15.9145 50.9956 15.7469 51.0863C13.7664 52.0529 6.22543 55.9649 4.33637 60.1034C2.20356 64.7857 4.67153 68.154 6.31684 68.6071C11.4051 70.0118 16.6153 67.4894 19.4336 63.3357C22.2368 59.1821 21.9016 53.7899 20.6067 51.3279L20.561 51.2373L22.1149 50.331C23.1204 49.742 24.1106 49.1982 24.9789 48.73C24.4914 50.0441 24.1411 51.5998 23.9582 53.8654C23.745 56.5238 24.8418 59.9675 26.2891 61.3269C26.9289 61.9159 27.6907 61.931 28.1629 61.931C29.8387 61.931 30.6004 60.5566 31.4383 58.9102C32.4743 56.9014 33.3883 54.5753 33.3883 54.5753C33.3883 54.5753 32.2457 60.8738 35.3688 60.8738C36.5114 60.8738 37.6539 59.4086 38.1719 58.6534V58.6685C38.1719 58.6685 38.2024 58.6232 38.2633 58.5175C38.3852 58.3363 38.4461 58.2305 38.4461 58.2305V58.2003C38.9032 57.4149 39.9239 55.6175 41.4473 52.642C43.4125 48.8055 45.3016 44.0024 45.3016 44.0024C45.3016 44.0024 45.4844 45.1805 46.0481 47.1139C46.3832 48.2618 47.1145 49.5154 47.6782 50.7389C47.2211 51.3732 46.9469 51.7357 46.9469 51.7357L46.9622 51.7508C46.5965 52.2342 46.1852 52.7477 45.7586 53.2612C44.2047 55.104 42.3461 57.2034 42.1024 57.8076C41.8129 58.5175 41.8739 59.0461 42.4375 59.4691C42.8489 59.7711 43.5801 59.8316 44.3571 59.7711C45.7586 59.6805 46.7336 59.3331 47.2211 59.1217C47.9829 58.8498 48.8512 58.442 49.6891 57.8378C51.2125 56.7201 52.1418 55.1342 52.0504 53.0196C52.0047 51.8566 51.6239 50.7086 51.1516 49.6211C51.2887 49.4248 51.4258 49.2284 51.5629 49.017C53.97 45.5279 55.8286 41.6915 55.8286 41.6915C55.8286 41.6915 56.0114 42.8696 56.575 44.8029C56.8645 45.7847 57.4434 46.8571 57.9614 47.9144C55.7067 49.742 54.2899 51.8566 53.8024 53.2461C52.9036 55.8139 53.6043 56.9769 54.9297 57.2488C55.5239 57.3696 56.377 57.0977 57.0168 56.8258C57.809 56.5691 58.7688 56.131 59.6524 55.4816C61.1758 54.3639 62.6383 52.8081 62.5622 50.7086C62.5164 49.742 62.2575 48.7904 61.9071 47.8842C63.8266 47.0987 66.3098 46.6456 69.4633 47.0232C76.2426 47.8086 77.5832 52.0076 77.3243 53.7597C77.0653 55.5118 75.6485 56.4784 75.1762 56.7805C74.7039 57.0675 74.5516 57.1732 74.5973 57.3847C74.6582 57.7019 74.8715 57.6868 75.2829 57.6264C75.8465 57.5357 78.8477 56.1915 78.9696 52.9592C79.1524 48.8055 75.1762 44.2743 68.1227 44.3196ZM15.8383 61.7951C13.5989 64.2269 10.4454 65.1482 9.10473 64.3628C7.65746 63.5321 8.22114 59.9524 10.9786 57.3696C12.6543 55.7988 14.8329 54.3488 16.2649 53.4576C16.5848 53.2613 17.0723 52.9743 17.6512 52.6269C17.7426 52.5665 17.8036 52.5363 17.8036 52.5363C17.9102 52.4758 18.0321 52.4003 18.1539 52.3248C19.1747 56.0102 18.1997 59.2425 15.8383 61.7951ZM32.2153 50.754C31.4383 52.642 29.793 57.4904 28.8028 57.2185C27.9497 56.992 27.4317 53.3217 28.6352 49.6967C29.2446 47.8691 30.5395 45.6941 31.3012 44.8482C32.5352 43.4889 33.8758 43.0357 34.211 43.5946C34.6071 44.3196 32.7028 49.5758 32.2153 50.754ZM45.7282 57.1581C45.393 57.3243 45.0883 57.4451 44.9512 57.3545C44.8446 57.2941 45.0883 57.0675 45.0883 57.0675C45.0883 57.0675 46.7793 55.2701 47.4497 54.4394C47.8305 53.956 48.2875 53.3972 48.775 52.7628V52.9441C48.775 55.1191 46.6727 56.5691 45.7282 57.1581ZM56.1485 54.8019C55.9047 54.6206 55.9352 54.0618 56.7579 52.3097C57.0778 51.6149 57.809 50.4519 59.0735 49.3493C59.2258 49.8024 59.3172 50.2404 59.302 50.6482C59.2868 53.367 57.3368 54.379 56.1485 54.8019Z" fill="#CF649A"/>
            </g>
            <defs>
                <clipPath id="clip0_2138_13175">
                    <rect width="78" height="58" fill="white" transform="translate(1 11)"/>
                </clipPath>
            </defs>
        </svg>
    );
}

export function IconFigma() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M40 40.625C40 37.9729 41.0536 35.4292 42.9289 33.5539C44.8043 31.6786 47.3479 30.625 50 30.625C52.6521 30.625 55.1957 31.6786 57.0711 33.5539C58.9464 35.4292 60 37.9729 60 40.625C60 43.2771 58.9464 45.8207 57.0711 47.6961C55.1957 49.5714 52.6521 50.625 50 50.625C47.3479 50.625 44.8043 49.5714 42.9289 47.6961C41.0536 45.8207 40 43.2771 40 40.625Z" fill="#1ABCFE"/>
            <path d="M20 60.625C20 57.9729 21.0536 55.4293 22.9289 53.5539C24.8042 51.6786 27.3479 50.625 30 50.625H40V60.625C40 63.2771 38.9464 65.8207 37.0711 67.6961C35.1958 69.5714 32.6521 70.625 30 70.625C27.3479 70.625 24.8042 69.5714 22.9289 67.6961C21.0536 65.8207 20 63.2771 20 60.625Z" fill="#0ACF83"/>
            <path d="M40 10.625V30.625H50C52.6521 30.625 55.1957 29.5714 57.0711 27.6961C58.9464 25.8208 60 23.2771 60 20.625C60 17.9729 58.9464 15.4292 57.0711 13.5539C55.1957 11.6786 52.6521 10.625 50 10.625H40Z" fill="#FF7262"/>
            <path d="M20 20.625C20 23.2771 21.0536 25.8208 22.9289 27.6961C24.8042 29.5714 27.3479 30.625 30 30.625H40V10.625H30C27.3479 10.625 24.8042 11.6786 22.9289 13.5539C21.0536 15.4292 20 17.9729 20 20.625Z" fill="#F24E1E"/>
            <path d="M20 40.625C20 43.2771 21.0536 45.8207 22.9289 47.6961C24.8042 49.5714 27.3479 50.625 30 50.625H40V30.625H30C27.3479 30.625 24.8042 31.6786 22.9289 33.5539C21.0536 35.4292 20 37.9729 20 40.625Z" fill="#A259FF"/>
        </svg>
    );
}

export function IconGSAP() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M40.7112 34.0733C40.7067 34.206 40.6496 34.3319 40.552 34.4242C40.4543 34.5165 40.3238 34.5681 40.188 34.568H34.7977C34.4445 34.568 34.1495 34.288 34.1495 33.9473C34.1495 32.4101 33.6053 31.6616 32.4921 31.6616C31.3789 31.6616 30.661 32.3299 30.64 33.4975C30.6161 34.7995 31.3694 35.9829 33.5137 38.0157C36.3367 40.6048 37.468 42.898 37.4136 45.9295C37.3248 50.8313 33.9156 54 28.7277 54C26.0784 54 24.0545 53.3075 22.7083 51.9411C21.3422 50.5532 20.7159 48.5167 20.8467 45.8884C20.8511 45.7555 20.9084 45.6296 21.0062 45.5372C21.104 45.4449 21.2348 45.3934 21.3708 45.3937H26.9463C27.0239 45.3949 27.1003 45.413 27.17 45.4466C27.2397 45.4801 27.3009 45.5284 27.3491 45.5879C27.3913 45.637 27.4221 45.6945 27.4396 45.7563C27.457 45.8182 27.4607 45.8829 27.4503 45.9463C27.3883 46.8945 27.5573 47.6029 27.9392 47.9949C28.1836 48.2488 28.5244 48.3776 28.9492 48.3776C29.9794 48.3776 30.5827 47.6664 30.6056 46.4269C30.6247 45.3555 30.2782 44.4165 28.3898 42.5172C25.9505 40.1876 23.7633 37.7805 23.8311 33.9959C23.8712 31.8007 24.7629 29.7931 26.3429 28.3427C28.0136 26.8101 30.2982 26 32.9504 26C35.6073 26.0187 37.6208 26.7597 38.9354 28.2027C40.1803 29.57 40.7789 31.5449 40.7131 34.0733H40.7112Z" fill="#0AE448"/>
            <path d="M57.4185 53.078L57.4538 27.0072C57.4548 26.942 57.4425 26.8771 57.4175 26.8166C57.3925 26.756 57.3554 26.7009 57.3084 26.6546C57.2613 26.6082 57.2053 26.5716 57.1435 26.5467C57.0818 26.5218 57.0156 26.5093 56.9488 26.5098H48.6066C48.3259 26.5098 48.2027 26.7459 48.1226 26.9018L36.0437 52.9184V52.9231L36.0389 52.9296C35.9052 53.2498 36.1592 53.5923 36.5134 53.5923H42.3447C42.6597 53.5923 42.8688 53.499 42.971 53.3048L44.129 50.5842C44.2713 50.2211 44.298 50.1875 44.7028 50.1875H50.2744C50.6621 50.1875 50.6697 50.195 50.664 50.5655L50.5389 53.0948C50.5379 53.1601 50.5502 53.2248 50.5751 53.2853C50.6 53.3458 50.637 53.4008 50.684 53.4472C50.7309 53.4935 50.7869 53.5302 50.8485 53.5551C50.9102 53.58 50.9763 53.5927 51.043 53.5923H56.9345C57.0074 53.5932 57.0796 53.5784 57.1461 53.5491C57.2126 53.5197 57.2716 53.4765 57.3192 53.4224C57.3602 53.3755 57.3903 53.3203 57.4075 53.2609C57.4246 53.2014 57.4284 53.1391 57.4185 53.078ZM47.0982 44.3738C47.0542 44.374 47.0102 44.3724 46.9664 44.3691C46.946 44.3675 46.9262 44.3614 46.9084 44.3515C46.8906 44.3416 46.8752 44.3279 46.8634 44.3115C46.8515 44.2952 46.8435 44.2765 46.8399 44.2568C46.8362 44.237 46.8371 44.2168 46.8423 44.1974C46.8538 44.16 46.87 44.1087 46.8938 44.0462L51.0716 33.94C51.1092 33.8403 51.1525 33.7428 51.2015 33.6479C51.2693 33.5116 51.3514 33.5023 51.3771 33.6031C51.3991 33.6871 50.8979 43.9799 50.8979 43.9799C50.8587 44.3654 50.8406 44.3812 50.452 44.413L47.102 44.3756H47.0943L47.0982 44.3738Z" fill="#0AE448"/>
            <path d="M69.3026 26.5096H64.8737C64.6398 26.5096 64.3773 26.631 64.3162 26.9035L58.1526 53.0648C58.1392 53.1238 58.1397 53.1851 58.1543 53.2438C58.1689 53.3025 58.197 53.3572 58.2366 53.4036C58.288 53.4628 58.352 53.5103 58.424 53.5428C58.4961 53.5754 58.5745 53.5922 58.6538 53.5922H64.1892C64.4871 53.5922 64.6904 53.4494 64.7458 53.202L65.4179 50.2368C65.4656 50.0054 65.3835 49.8262 65.1716 49.7179C65.0721 49.6678 64.9728 49.6174 64.8737 49.5667L63.9143 49.0786L62.9596 48.5914L62.5901 48.4038C62.56 48.3891 62.5348 48.3664 62.5174 48.3384C62.5 48.3103 62.4911 48.2779 62.4917 48.2451C62.493 48.1965 62.5137 48.1503 62.5494 48.1164C62.5851 48.0826 62.633 48.0638 62.6827 48.064L65.7158 48.0771C66.6227 48.0818 67.5307 48.0192 68.4233 47.8587C74.7053 46.7247 78.8773 41.806 78.9976 35.1131C79.1007 29.4011 75.8394 26.5068 69.3074 26.5068L69.3026 26.5096ZM67.7951 42.0776H67.6767C67.4104 42.0776 67.3636 42.0496 67.356 42.0403C67.3512 42.0338 69.105 34.5055 69.1059 34.4952C69.1508 34.2778 69.1489 34.1527 69.0114 34.079C68.8357 33.9838 66.2752 32.664 66.2752 32.664C66.2455 32.6487 66.2206 32.6256 66.2036 32.5972C66.1866 32.5688 66.178 32.5364 66.1788 32.5035C66.1801 32.4556 66.2004 32.41 66.2356 32.3765C66.2707 32.343 66.3178 32.3243 66.3669 32.3243H70.4158C71.676 32.3616 72.3786 33.4639 72.3452 35.3464C72.287 38.6047 70.7022 41.9628 67.7951 42.0776Z" fill="#0AE448"/>
            <path d="M23.7318 39.0785V39.0906L22.7055 43.4456C22.6501 43.692 22.3981 43.8721 22.1078 43.8721H20.8677C20.823 43.8723 20.7795 43.8866 20.7438 43.9128C20.708 43.939 20.6818 43.9758 20.6691 44.0177C19.5254 47.8108 17.9768 50.4176 15.9319 51.9828C14.1924 53.3156 12.0481 53.9372 9.18309 53.9372C6.60921 53.9372 4.87356 53.128 3.40141 51.5301C1.45573 49.4189 0.651868 45.9618 1.13877 41.7973C2.01709 33.9788 6.16527 26.0893 14.1523 26.0893C16.582 26.0688 18.4895 26.8014 19.8166 28.264C21.22 29.8114 21.9322 32.141 21.936 35.1912C21.9325 35.3249 21.8758 35.452 21.7779 35.5454C21.68 35.6388 21.5487 35.6912 21.4119 35.6914H15.5595C15.457 35.6883 15.3596 35.647 15.2873 35.5758C15.215 35.5046 15.1734 35.409 15.171 35.3088C15.1232 33.1994 14.4836 32.1728 13.2119 32.1728C10.9684 32.1728 9.64516 35.1501 8.94346 36.8002C7.96298 39.1056 7.46367 41.6097 7.562 44.1036C7.60878 45.2646 7.79972 46.897 8.92723 47.5737C9.9268 48.1729 11.3531 47.7753 12.2171 47.1117C13.0802 46.4472 13.7752 45.2982 14.0673 44.2501C14.1084 44.1045 14.1113 43.9916 14.0721 43.9412C14.0311 43.8889 13.9175 43.8768 13.8306 43.8768H12.3279C12.2483 43.877 12.1697 43.8603 12.0975 43.8277C12.0252 43.7951 11.9612 43.7475 11.9097 43.6882C11.8702 43.6419 11.8422 43.5874 11.8278 43.5288C11.8134 43.4702 11.813 43.4091 11.8267 43.3504L12.8539 38.988C12.9045 38.7649 13.1117 38.596 13.3666 38.5661V38.5549H23.2287C23.2516 38.5549 23.2754 38.5549 23.2974 38.5596C23.5533 38.5922 23.7337 38.8246 23.7289 39.0785H23.7318Z" fill="#0AE448"/>
        </svg>
    );
}

export function IconHTML() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M68.3829 10.2537C68.1204 9.96387 67.743 9.7998 67.3493 9.7998H12.6508C12.2571 9.7998 11.8797 9.96387 11.6172 10.2537C11.3493 10.5436 11.218 10.9318 11.2563 11.3256L16.1836 66.5217C16.2329 67.1014 16.6375 67.5826 17.1954 67.7467L39.6118 74.1451C39.7321 74.1834 39.8633 74.1998 39.9946 74.1998C40.1258 74.1998 40.2516 74.1834 40.3774 74.1451L62.8102 67.7467C63.368 67.5826 63.7672 67.1014 63.8219 66.5217L68.7438 11.3256C68.7821 10.9318 68.6508 10.5436 68.3829 10.2537ZM56.5868 29.285H30.2493L30.8782 36.4107H55.9524L54.0711 57.4709L39.9891 61.7256L39.8524 61.6818L25.9344 57.4654L25.1743 48.885H31.9938L32.2891 52.2154L40.0711 53.8451L47.711 52.2154L48.5258 43.2029H24.6657L22.8118 22.4764H57.1938L56.5868 29.285Z" fill="#FB4A0B"/>
        </svg>

    );
}

export function IconJS() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M65 5H15C9.47715 5 5 9.47715 5 15V65C5 70.5229 9.47715 75 15 75H65C70.5229 75 75 70.5229 75 65V15C75 9.47715 70.5229 5 65 5Z"
                fill="#F7DF1E"/>
            <path
                d="M35.7586 39.8189H41.2302V58.0746C41.2302 59.7621 40.851 61.228 40.0924 62.4723C39.3424 63.7166 38.2984 64.6754 36.9603 65.3487C35.6223 66.022 34.0669 66.3587 32.2941 66.3587C30.7174 66.3587 29.2856 66.0817 27.9987 65.5277C26.7203 64.9652 25.7061 64.1129 24.9561 62.9709C24.2061 61.8203 23.8353 60.3757 23.8439 58.6371H29.3538C29.3708 59.3274 29.5115 59.9197 29.7757 60.4141C30.0484 60.8998 30.4191 61.2748 30.8879 61.5391C31.3652 61.7947 31.9277 61.9226 32.5754 61.9226C33.2572 61.9226 33.8325 61.7777 34.3012 61.4879C34.7785 61.1896 35.1407 60.755 35.3879 60.1839C35.635 59.6129 35.7586 58.9098 35.7586 58.0746V39.8189ZM60.01 47.3487C59.9078 46.3175 59.4689 45.5163 58.6933 44.9453C57.9177 44.3743 56.8652 44.0888 55.5356 44.0888C54.6322 44.0888 53.8694 44.2166 53.2473 44.4723C52.6251 44.7195 52.1478 45.0646 51.8154 45.5078C51.4916 45.951 51.3296 46.4538 51.3296 47.0163C51.3126 47.4851 51.4106 47.8942 51.6237 48.2436C51.8453 48.593 52.1478 48.8956 52.5313 49.1513C52.9149 49.3984 53.3581 49.6158 53.8609 49.8033C54.3637 49.9822 54.9007 50.1356 55.4717 50.2635L57.824 50.826C58.966 51.0817 60.0143 51.4226 60.9689 51.8487C61.9234 52.2748 62.7501 52.799 63.449 53.4212C64.1478 54.0433 64.689 54.7763 65.0725 55.62C65.4646 56.4638 65.6649 57.4311 65.6734 58.522C65.6649 60.1243 65.2558 61.5135 64.4461 62.6896C63.645 63.8572 62.4859 64.7649 60.9689 65.4126C59.4603 66.0518 57.6407 66.3714 55.51 66.3714C53.3964 66.3714 51.5555 66.0476 49.9873 65.3998C48.4277 64.7521 47.2089 63.7933 46.3311 62.5234C45.4617 61.245 45.0058 59.6641 44.9632 57.7805H50.3197C50.3794 58.6584 50.6308 59.3913 51.074 59.9794C51.5257 60.5589 52.1265 60.9979 52.8765 61.2962C53.635 61.5859 54.4916 61.7308 55.4461 61.7308C56.3836 61.7308 57.1975 61.5944 57.8879 61.3217C58.5867 61.049 59.1279 60.6697 59.5115 60.1839C59.895 59.6981 60.0867 59.1399 60.0867 58.5092C60.0867 57.9212 59.912 57.4268 59.5626 57.0263C59.2217 56.6257 58.7189 56.2848 58.0541 56.0035C57.3978 55.7223 56.5924 55.4666 55.6379 55.2365L52.787 54.5206C50.5796 53.9837 48.8367 53.1442 47.5583 52.0021C46.2799 50.8601 45.645 49.3217 45.6535 47.3871C45.645 45.8018 46.0669 44.4169 46.9191 43.2322C47.7799 42.0476 48.9603 41.1229 50.4603 40.4581C51.9603 39.7933 53.6649 39.4609 55.574 39.4609C57.5171 39.4609 59.2132 39.7933 60.662 40.4581C62.1194 41.1229 63.2529 42.0476 64.0626 43.2322C64.8723 44.4169 65.2899 45.7891 65.3154 47.3487H60.01Z"
                fill="black"/>
        </svg>
    );
}

export function IconReact() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M40.5 47.1481C44.4212 47.1481 47.6 43.9473 47.6 39.9988C47.6 36.0504 44.4212 32.8496 40.5 32.8496C36.5788 32.8496 33.4 36.0504 33.4 39.9988C33.4 43.9473 36.5788 47.1481 40.5 47.1481Z"
                fill="#149ECA"/>
            <path
                d="M40.5 56.0856C60.1061 56.0856 76 48.8838 76 39.9998C76 31.1159 60.1061 23.9141 40.5 23.9141C20.8939 23.9141 5 31.1159 5 39.9998C5 48.8838 20.8939 56.0856 40.5 56.0856Z"
                stroke="#149ECA"/>
            <path
                d="M26.6652 48.0429C36.4683 65.14 50.6092 75.3991 58.25 70.9571C65.8907 66.5151 64.1378 49.0542 54.3347 31.9571C44.5317 14.86 30.3907 4.60093 22.75 9.04289C15.1092 13.4849 16.8622 30.9458 26.6652 48.0429Z"
                stroke="#149ECA"/>
            <path
                d="M26.6652 31.9573C16.8622 49.0544 15.1092 66.5153 22.75 70.9573C30.3907 75.3992 44.5317 65.1402 54.3347 48.043C64.1378 30.9459 65.8907 13.485 58.25 9.04305C50.6092 4.60108 36.4683 14.8601 26.6652 31.9573Z"
                stroke="#149ECA"/>
        </svg>
    );
}

export function IconWebpack() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M40 1.25L73.75 20.6161V59.3573L40 78.7235L6.25 59.3573V20.6161L40 1.25Z" fill="white"/>
            <path
                d="M67.7241 58.0209L41.1166 73.2909V61.4005L57.695 52.1465L67.7241 58.0209ZM69.5467 56.3488V24.4199L59.8142 30.1263V50.6513L69.5467 56.3488ZM12.1718 58.0209L38.7794 73.2909V61.4005L22.1921 52.1465L12.1718 58.0209ZM10.3491 56.3488V24.4199L20.0816 30.1263V50.6513L10.3491 56.3488ZM11.4916 22.3497L38.7794 6.69043V18.1828L21.2939 27.941L21.1544 28.0207L11.4916 22.3497ZM68.4044 22.3497L41.1166 6.69043V18.1828L58.602 27.9322L58.7416 28.0118L68.4044 22.3497Z"
                fill="#8ED6FB"/>
            <path
                d="M38.7786 58.6933L22.4182 49.5631V31.4886L38.7786 41.07V58.6933ZM41.1158 58.6933L57.4763 49.572V31.4886L41.1158 41.07V58.6933ZM23.5257 29.4008L39.9472 20.2441L56.3687 29.4008L39.9472 39.0175L23.5257 29.4008Z"
                fill="#1C78C0"/>
        </svg>
    );
}

export function IconAPI() {
    return (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <ellipse cx="33.125" cy="36.2134" rx="22.5503" ry="22.6909" fill="#002B51"/>
            <ellipse cx="55.7047" cy="33.9121" rx="13.1544" ry="13.2364" fill="#002B51"/>
            <rect y="31.3662" width="80" height="28.6338" rx="14.3169" fill="#002B51"/>
            <path
                d="M10.0443 44.9528C10.6 44.9528 11.0014 44.7913 11.2483 44.4682C11.4953 44.1451 11.6188 43.7226 11.6188 43.2007V40.3863C11.6188 39.8519 11.6867 39.3798 11.8226 38.9697C11.9584 38.5472 12.1807 38.1931 12.4894 37.9073C12.8105 37.6215 13.2304 37.404 13.749 37.2549C14.28 37.1058 14.9345 37.0312 15.7125 37.0312H16.083V39.2493H15.6199C15.0642 39.2493 14.6875 39.3735 14.4899 39.6221C14.3047 39.8706 14.2121 40.2558 14.2121 40.7777V43.2567C14.2121 43.9028 14.1257 44.4496 13.9528 44.8969C13.7799 45.3442 13.4341 45.7294 12.9155 46.0525C13.4341 46.3756 13.7799 46.7608 13.9528 47.2081C14.1257 47.6555 14.2121 48.2022 14.2121 48.8484V51.3273C14.2121 51.8492 14.3047 52.2344 14.4899 52.483C14.6875 52.7315 15.0642 52.8557 15.6199 52.8557H16.083V55.0738H15.7125C14.9345 55.0738 14.28 54.9992 13.749 54.8501C13.2304 54.701 12.8105 54.4835 12.4894 54.1977C12.1807 53.9119 11.9584 53.5578 11.8226 53.1353C11.6867 52.7253 11.6188 52.2531 11.6188 51.7188V48.9043C11.6188 48.3824 11.4953 47.9599 11.2483 47.6368C11.0014 47.3137 10.6 47.1522 10.0443 47.1522V44.9528Z" fill="white"/>
            <path d="M21.8826 38.5596C23.809 38.5596 25.2847 38.9076 26.3097 39.6034C27.3346 40.2869 27.8471 41.3555 27.8471 42.8093C27.8471 43.7164 27.6372 44.4558 27.2173 45.0274C26.8098 45.5865 26.2171 46.0277 25.4391 46.3507C25.6984 46.6738 25.9701 47.0466 26.2541 47.4691C26.5381 47.8791 26.816 48.314 27.0877 48.7738C27.3717 49.2211 27.6434 49.6933 27.9027 50.1904C28.162 50.675 28.4028 51.1534 28.6251 51.6256H25.3835C25.1489 51.2031 24.9081 50.7744 24.6611 50.3395C24.4265 49.9046 24.1795 49.4821 23.9201 49.072C23.6732 48.662 23.4262 48.2768 23.1792 47.9164C22.9322 47.5436 22.6852 47.2081 22.4383 46.9099H21.012V51.6256H18.1223V38.8951C18.7521 38.7709 19.4004 38.6839 20.0673 38.6342C20.7465 38.5845 21.3516 38.5596 21.8826 38.5596ZM22.0493 41.0386C21.8393 41.0386 21.6479 41.0448 21.475 41.0573C21.3145 41.0697 21.1601 41.0821 21.012 41.0945V44.5987H21.827C22.9137 44.5987 23.6917 44.462 24.1609 44.1886C24.6302 43.9152 24.8648 43.4493 24.8648 42.7907C24.8648 42.157 24.624 41.7096 24.1424 41.4487C23.6732 41.1753 22.9754 41.0386 22.0493 41.0386Z" fill="white"/>
            <path d="M30.8478 51.6256V38.7088H39.5168V41.1505H33.7374V43.6854H38.8685V46.0711H33.7374V49.1839H39.9428V51.6256H30.8478Z" fill="white"/>
            <path d="M45.9527 49.4075C46.3602 49.4075 46.6936 49.3765 46.9529 49.3143C47.2246 49.2398 47.4407 49.1466 47.6013 49.0347C47.7618 48.9105 47.8729 48.7676 47.9347 48.606C47.9964 48.4445 48.0273 48.2643 48.0273 48.0655C48.0273 47.643 47.8297 47.2951 47.4346 47.0217C47.0394 46.7359 46.3602 46.4315 45.397 46.1084C44.9771 45.9593 44.5572 45.7916 44.1374 45.6052C43.7175 45.4064 43.3409 45.1641 43.0074 44.8783C42.674 44.58 42.4023 44.2259 42.1924 43.8158C41.9825 43.3933 41.8775 42.8839 41.8775 42.2874C41.8775 41.691 41.9886 41.1567 42.2109 40.6845C42.4332 40.1999 42.7481 39.7898 43.1556 39.4543C43.5631 39.1188 44.0571 38.8641 44.6375 38.6901C45.2179 38.5037 45.8724 38.4105 46.601 38.4105C47.4654 38.4105 48.2125 38.5037 48.8423 38.6901C49.4721 38.8765 49.9908 39.0815 50.3983 39.3052L49.5648 41.5978C49.2066 41.4114 48.8053 41.2499 48.3607 41.1132C47.9285 40.9641 47.4037 40.8895 46.7862 40.8895C46.0947 40.8895 45.5946 40.9889 45.2858 41.1877C44.9895 41.3741 44.8413 41.6661 44.8413 42.0638C44.8413 42.2999 44.8968 42.4987 45.008 42.6602C45.1191 42.8218 45.2735 42.9709 45.4711 43.1076C45.681 43.2318 45.9156 43.3499 46.175 43.4617C46.4466 43.5611 46.743 43.6667 47.0641 43.7786C47.7309 44.0271 48.3113 44.2756 48.8053 44.5241C49.2993 44.7602 49.7068 45.0398 50.0278 45.3629C50.3613 45.6859 50.6082 46.0649 50.7688 46.4998C50.9293 46.9348 51.0096 47.4629 51.0096 48.0842C51.0096 49.2895 50.5897 50.2276 49.75 50.8986C48.9103 51.5572 47.6445 51.8865 45.9527 51.8865C45.3846 51.8865 44.8721 51.8492 44.4152 51.7747C43.9583 51.7125 43.5508 51.6318 43.1927 51.5324C42.8469 51.433 42.5444 51.3273 42.285 51.2155C42.038 51.1037 41.8281 50.998 41.6552 50.8986L42.4703 48.5874C42.8531 48.7986 43.3223 48.9913 43.878 49.1652C44.4461 49.3268 45.1376 49.4075 45.9527 49.4075Z" fill="white"/>
            <path d="M62.7627 38.7088V41.1877H58.8913V51.6256H56.0017V41.1877H52.1303V38.7088H62.7627Z" fill="white"/>
            <path d="M69.7323 47.1522C69.1766 47.1522 68.7753 47.3137 68.5283 47.6368C68.2813 47.9599 68.1579 48.3824 68.1579 48.9043V51.7188C68.1579 52.2531 68.0899 52.7253 67.9541 53.1353C67.8183 53.5578 67.5898 53.9119 67.2687 54.1977C66.96 54.4835 66.5401 54.701 66.0091 54.8501C65.4905 54.9992 64.8421 55.0738 64.0642 55.0738H63.6937V52.8557H64.1568C64.7125 52.8557 65.083 52.7315 65.2682 52.483C65.4658 52.2344 65.5646 51.8492 65.5646 51.3273V48.8484C65.5646 48.2022 65.651 47.6555 65.8239 47.2081C65.9968 46.7608 66.3426 46.3756 66.8612 46.0525C66.3426 45.7294 65.9968 45.3442 65.8239 44.8969C65.651 44.4496 65.5646 43.9028 65.5646 43.2567V40.7777C65.5646 40.2558 65.4658 39.8706 65.2682 39.6221C65.083 39.3735 64.7125 39.2493 64.1568 39.2493H63.6937V37.0312H64.0642C64.8421 37.0312 65.4905 37.1058 66.0091 37.2549C66.5401 37.404 66.96 37.6215 67.2687 37.9073C67.5898 38.1931 67.8183 38.5472 67.9541 38.9697C68.0899 39.3798 68.1579 39.8519 68.1579 40.3863V43.2007C68.1579 43.7226 68.2813 44.1451 68.5283 44.4682C68.7753 44.7913 69.1766 44.9528 69.7323 44.9528V47.1522Z" fill="white"/>
        </svg>
    );
}