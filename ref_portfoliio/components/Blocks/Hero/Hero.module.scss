@import '/assets/media-queries.scss';

.hero {
  position: relative;

  .inner {
    position: relative;
    z-index: 1;
    overflow: hidden;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100dvh;
    padding: calc(var(--header-height) + (var(--x-spacing) / 2)) var(--x-spacing) calc(var(--x-spacing) * 2);

    text-align: center;
  }
}

.title {
  margin-bottom: 3rem;
  @include respond-above(xl) {
    max-width: 68vw;
  }
  .splitLine {
    overflow: hidden;
    margin: calc(-1 * 4rem) 0;
    padding: 2rem 0;
    &:nth-child(2) {
      .splitWords{
        background: -webkit-linear-gradient(hsl(var(--color-white)), hsl(200, 60, 82));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  h1 {
    font-weight: var(--fw-bold);
    font-size: var(--fs-xml);
    line-height: 1;
    opacity: 0;
    transition: all 0.3s ease-in;
    color: hsl(var(--color-white));
    @include respond-above(xl) {
      max-width: 68vw;
      line-height: 0.9;
    }
  }

  .icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 30rem;
    height: 9rem;
    border-radius: 100vw;
    vertical-align: baseline;
    overflow: hidden;
    margin: 0 1.5rem;
    z-index: 111;
    position: relative;
    @include respond-below(xml) {
      height: 7.8rem;
      width: 24rem;
    }
    @include respond-below(lg) {
      height: 6.4rem;
      width: 20rem;
    }
    @include respond-below(md) {
      margin: 1.5rem auto;
      display: flex;
    }
    .heroImg {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transform: scale(1.6);
      transform-origin: left center;
      border-radius: 100vw;
    }
    .reveal {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, #b9b5db, hsl(var(--color-bg)));
      z-index: 1;
      border-radius: 100vw;
    }
  }

  p{
    font-size: var(--fs-md);
    font-weight: var(--fw-light);
    color: hsl(var(--color-white));
    max-width: 70rem;
    margin: 6rem auto 0;
  }
}

.description {
  margin-bottom: 3rem;
  font-size: var(--fs-sm);
  color: hsl(var(--color-white));
  max-width: 50rem;
}

.background {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  inset: 0;
  z-index: -1;

  > * {
    position: absolute;
  }

  .noise {
    inset: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: url('/noise.png') repeat;
    pointer-events: none;
  }

  .line {
    width: 50%;
    height: 100%;
    display: flex;

    &.lineLeft {
      left: 0;
    }

    &.lineRight {
      right: 0;
    }

    svg {
      width: 100%;
      height: auto;
      margin-top: auto;
    }
  }
}

.fakeContainer {
  position: absolute;
  inset: 0;
  opacity: .5;
  width: 100%;
  height: 100%;
  pointer-events: none;
  user-select: none;
  z-index: -999999;
}

.particlesBG {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  user-select: none;

  > * {
    width: 100% !important;
    height: 100% !important;
  }
}