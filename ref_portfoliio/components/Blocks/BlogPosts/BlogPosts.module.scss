.section{
  position: relative;
  padding: var(--y-spacing) 0;
  background: hsl(var(--color-bg));
}

.header{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6rem;
}

.grid{
  display: grid;
  grid-gap: 9rem;
  grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));

  > * {
    order: 2;
    &:nth-child(1){
      order: -1;
      grid-row: 1 / 3;
    }
    &:nth-child(2){
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      order: 1;
    }
    &:nth-child(3){
      order: 0;
    }
  }
}

.badge{

}