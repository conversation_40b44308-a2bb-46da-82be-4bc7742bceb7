@import '/assets/media-queries.scss';

.section{
  position: relative;
  padding: var(--x-spacing) 0;
  background: hsl(var(--color-bg-alt));
}
.grid{
  display: grid;
  grid-template-columns: 1fr;
  padding: var(--y-spacing) var(--x-spacing) 0;
  gap: 6rem;

  overflow: hidden;
}

.sphereWrapper{
  --size: 16dvw;
  position: absolute;
  bottom: calc(-1 * var(--size) / 2);
  left: calc(50% - var(--size) / 2);
  .sphere{
    transition: all .3s ease-in-out;
    transform-origin: 50% 50%;

    width: var(--size);
    height: var(--size);
    border-radius: 100%;
    background: linear-gradient(113deg, #DECAFF 11.44%, #691EE2 60.27%);
    box-shadow: -35.036px -50.051px 80.1px 0px rgba(172, 60, 225, 0.76) inset, 0px 20.02px 40px 0px rgba(194, 255, 255, 0.25) inset, 0px 0px 24px 0px rgba(255, 255, 255, 0.26) inset;

    z-index: 1;
    pointer-events: none;
    user-select: none;

    &:before, &:after{
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
    }
    &:before{
      width: 120%;
      height: 120%;
      border-radius: 100%;
      filter: blur(8rem);
      background: linear-gradient(180deg, rgba(0, 194, 255, 0.00) 0%, #9829ff 100%);
      transform: translate(-50%, -80%);
    }
    &:after{
      width: 66%;
      height: 100%;
      filter: blur(8rem);
      background: linear-gradient(180deg, rgba(24, 75, 255, 0.00) 0%, #174AFF 100%);
      transform: translate(-50%, -50%);
    }

    &.isActive{
      transform: scale(0.8);
    }
  }
}

.blobs{
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.blob{
  pointer-events: none;
  user-select: none;
  &.blobV1 {
    top: 20%;
    height: 20%;
  }
  &.blobV2 {
    top: 20%;
    height: 40%;
  }
}
.dragMe{
  user-select: none;
  pointer-events: none;
  position: absolute;
  top: 0;
  right: 0;
  width: 18dvw;
  svg{
    width: 100%;
    height: auto;
  }
}

.circularCarouselWrapper{
  position: relative;
  --circSize: 65dvw;
  --cardSize: calc(var(--circSize) / 5.2);
  padding: calc(var(--cardSize) / 2);
  margin: 0 auto 0 auto;
  width: fit-content;
  height: calc(var(--circSize) * 0.66);
  overflow: hidden;

  @include respond-below(sm) {
    --circSize: 150dvw;
    --cardSize: calc(var(--circSize) / 4);
    padding: calc(var(--cardSize) / 2);
    height: calc(var(--circSize) * 0.7);
    left: -50dvw;
  }

  .circularCarousel{
    width: var(--circSize);
    height: var(--circSize);
    svg{
      user-select: none;
      pointer-events: none;
      width: 100%;
      height: 100%;
      visibility: hidden;
      opacity: 0;
    }
  }
  .box {
    width: var(--cardSize);
    height: var(--cardSize);
    display: flex;
    justify-content: center;
    align-items: center;
    background: conic-gradient(#f0f0f0, hsla(var(--color-white), 1));
    border-radius: 2rem;

    .image{
      max-width: calc(var(--cardSize) - (var(--cardSize) / 2));
    }
  }
  .collisionDiv{
    --width: 0.2rem;
    position: absolute;
    z-index: 100;
    top: calc(var(--cardSize) - var(--cardSize) / 2);
    left: calc(50% - var(--width) / 2);
    width: var(--width);
    height: var(--width);
  }

  .circularDescriptions{
    position: absolute;
    left: 50%;
    top: calc(var(--cardSize) * 1.5);
    transform: translate(-50%, 20%);

    display: flex;
    flex-direction: column;
    gap: 3rem;
    text-align: center;

    opacity: 0;
    transition: all 0.3s ease-in-out;
    @include respond-below(sm) {
      padding: 0 1.5rem;
      top: calc(var(--cardSize) * 1.3);
      gap: 1.5rem;
    }
    &.isActive {
      opacity: 1;
      transform: translate(-50%, 0%);
    }
  }

}
.title{
  font-size: var(--fs-sm);
  color: hsla(var(--color-white), 0.8);
  font-weight: var(--fw-bold);
}
.description{
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 40ch;
  font-size: var(--fs-xs);
  color: hsl(var(--color-body));
}