[{"icon": "IconJS", "image": "/icon/icon-js.svg", "title": "JavaScript", "subtitle": "Van<PERSON>, j<PERSON><PERSON>y", "description": "I bring websites to life with JavaScript, adding interactive features and optimizing performance with clean and efficient code."}, {"icon": "IconReact", "image": "/icon/icon-react.svg", "title": "React JS", "subtitle": "<PERSON>.js, <PERSON><PERSON><PERSON>", "description": "Using React, I craft scalable user interfaces with components, state management, and virtual DOM manipulation for seamless web applications."}, {"icon": "IconCSS", "image": "/icon/icon-css.svg", "title": "CSS", "subtitle": "Animations, Sass", "description": "I prioritize pixel perfection, paying close attention to details for visually polished and precisely aligned designs, ensuring a seamless user experience."}, {"icon": "IconHTML", "image": "/icon/icon-html.svg", "title": "HTML", "subtitle": "<PERSON><PERSON><PERSON>", "description": "Beyond markup, HTML is crucial for an inclusive web. I structure content carefully for universal accessibility."}, {"icon": "IconFigma", "image": "/icon/icon-figma.svg", "title": "Design", "subtitle": "Figma & Photoshop", "description": "While not a full-time UI designer, I have an eye for aesthetics, creating visually appealing and user-friendly interfaces."}, {"icon": "IconWebpack", "image": "/icon/icon-webpack.svg", "title": "Development Env.", "subtitle": "Webpack", "description": "I use tools like Webpack, Vite, Parcel, or Gulp to streamline workflows, automating development for efficiency."}, {"icon": "IconAPI", "image": "/icon/icon-api.svg", "title": "REST APIs", "subtitle": "Axios x Fetch", "description": "I integrate data and functionality seamlessly with REST APIs, creating dynamic web applications for enhanced performance."}, {"icon": "IconGSAP", "image": "/icon/icon-gsap.svg", "title": "GSAP", "subtitle": "Scroll Animations", "description": "I use GSAP for captivating animations that bring web pages to life, enhancing the overall user experience."}, {"icon": "IconSANITY", "image": "/icon/icon-sanity.svg", "title": "Sanity.io", "subtitle": "Headless CMS, Gatsby", "description": "I use Sanity CMS for building website pages."}, {"icon": "IconNode", "image": "/icon/icon-node.svg", "title": "Node", "subtitle": "", "description": "I use it to write server-side applications with access to the operating system, file system, and everything else required to build fully-functional applications.."}, {"icon": "IconMongoDB", "image": "/icon/icon-mongodb.svg", "title": "MongoDB", "subtitle": "", "description": "I use MongoDB for developing scalable applications with evolving data schemas."}, {"icon": "IconDjango", "image": "/icon/icon-django.svg", "title": "Django", "subtitle": "Python", "description": "I use Django with Python to build secure, scalable web applications quickly, leveraging its powerful features and built-in admin interface."}]