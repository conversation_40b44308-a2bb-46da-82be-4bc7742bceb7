/*!
 * @gsap/react 2.1.0
 * https://gsap.com
 *
 * @license Copyright 2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for Club GSAP members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("gsap")):"function"==typeof define&&define.amd?define(["exports","react","gsap"],t):t((e=e||self).window=e.window||{},e.react,e.gsap)}(this,function(e,a,t){"use strict";t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;let u="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,p=e=>e&&!Array.isArray(e)&&"object"==typeof e,l=[],y={},w=t;t=(e,t=l)=>{let n=y,{scope:o,revertOnUpdate:r}=(p(e)?(n=e,e=null,t="dependencies"in n?n.dependencies:l):p(t)&&(t="dependencies"in(n=t)?n.dependencies:l),n),[d,i]=a.useState(!1);e&&"function"!=typeof e&&console.warn("First parameter must be a function or config object");const s=w.context(()=>{},o),c=()=>s.revert(),f=t&&t.length&&!r;return u(()=>{if(e&&s.add(e,o),!f||!d)return c},t),f&&u(()=>(i(!0),c),l),{context:s,contextSafe:e=>s.add(null,e)}};t.register=e=>{w=e},t.headless=!0,e.useGSAP=t,Object.defineProperty(e,"__esModule",{value:!0})});
