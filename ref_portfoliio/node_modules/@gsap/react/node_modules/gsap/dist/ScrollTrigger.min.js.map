{"version": 3, "file": "ScrollTrigger.min.js", "sources": ["../src/Observer.js", "../src/ScrollTrigger.js"], "sourcesContent": ["/*!\n * Observer 3.12.5\n * https://gsap.com\n *\n * @license Copyright 2008-2024, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license or for\n * Club GSAP members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _clamp, _win, _doc, _docEl, _body, _isTouch, _pointerType, ScrollTrigger, _root, _normalizer, _eventTypes, _context,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_startup = 1,\n\t_observers = [],\n\t_scrollers = [],\n\t_proxies = [],\n\t_getTime = Date.now,\n\t_bridge = (name, value) => value,\n\t_integrate = () => {\n\t\tlet core = ScrollTrigger.core,\n\t\t\tdata = core.bridge || {},\n\t\t\tscrollers = core._scrollers,\n\t\t\tproxies = core._proxies;\n\t\tscrollers.push(..._scrollers);\n\t\tproxies.push(..._proxies);\n\t\t_scrollers = scrollers;\n\t\t_proxies = proxies;\n\t\t_bridge = (name, value) => data[name](value);\n\t},\n\t_getProxyProp = (element, property) => ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property],\n\t_isViewport = el => !!~_root.indexOf(el),\n\t_addListener = (element, type, func, passive, capture) => element.addEventListener(type, func, {passive: passive !== false, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_onScroll = () => (_normalizer && _normalizer.isPressed) || _scrollers.cache++,\n\t_scrollCacheFunc = (f, doNotCache) => {\n\t\tlet cachingFunc = value => { // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n\t\t\tif (value || value === 0) {\n\t\t\t\t_startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\t\t\t\tlet isNormalizing = _normalizer && _normalizer.isPressed;\n\t\t\t\tvalue = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\t\t\t\tf(value);\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tisNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n\t\t\t} else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tcachingFunc.v = f();\n\t\t\t}\n\t\t\treturn cachingFunc.v + cachingFunc.offset;\n\t\t};\n\t\tcachingFunc.offset = 0;\n\t\treturn f && cachingFunc;\n\t},\n\t_horizontal = {s: _scrollLeft, p: \"left\", p2: \"Left\", os: \"right\", os2: \"Right\", d: \"width\", d2: \"Width\", a: \"x\", sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0})},\n\t_vertical = {s: _scrollTop, p: \"top\", p2: \"Top\", os: \"bottom\", os2: \"Bottom\", d: \"height\", d2: \"Height\", a: \"y\", op: _horizontal, sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0})},\n\t_getTarget = (t, self) => ((self && self._ctx && self._ctx.selector) || gsap.utils.toArray)(t)[0] || (typeof(t) === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null),\n\n\t_getScrollFunc = (element, {s, sc}) => { // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n\t\t_isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\t\tlet i = _scrollers.indexOf(element),\n\t\t\toffset = sc === _vertical.sc ? 1 : 2;\n\t\t!~i && (i = _scrollers.push(element) - 1);\n\t\t_scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\t\tlet prev = _scrollers[i + offset],\n\t\t\tfunc = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function(value) { return arguments.length ? (element[s] = value) : element[s]; })));\n\t\tfunc.target = element;\n\t\tprev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\t\treturn func;\n\t},\n\t_getVelocityProp = (value, minTimeRefresh, useDelta) => {\n\t\tlet v1 = value,\n\t\t\tv2 = value,\n\t\t\tt1 = _getTime(),\n\t\t\tt2 = t1,\n\t\t\tmin = minTimeRefresh || 50,\n\t\t\tdropToZeroTime = Math.max(500, min * 3),\n\t\t\tupdate = (value, force) => {\n\t\t\t\tlet t = _getTime();\n\t\t\t\tif (force || t - t1 > min) {\n\t\t\t\t\tv2 = v1;\n\t\t\t\t\tv1 = value;\n\t\t\t\t\tt2 = t1;\n\t\t\t\t\tt1 = t;\n\t\t\t\t} else if (useDelta) {\n\t\t\t\t\tv1 += value;\n\t\t\t\t} else { // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n\t\t\t\t\tv1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n\t\t\t\t}\n\t\t\t},\n\t\t\treset = () => { v2 = v1 = useDelta ? 0 : v1; t2 = t1 = 0; },\n\t\t\tgetVelocity = latestValue => {\n\t\t\t\tlet tOld = t2,\n\t\t\t\t\tvOld = v2,\n\t\t\t\t\tt = _getTime();\n\t\t\t\t(latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n\t\t\t\treturn (t1 === t2 || t - t2 > dropToZeroTime) ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n\t\t\t};\n\t\treturn {update, reset, getVelocity};\n\t},\n\t_getEvent = (e, preventDefault) => {\n\t\tpreventDefault && !e._gsapAllow && e.preventDefault();\n\t\treturn e.changedTouches ? e.changedTouches[0] : e;\n\t},\n\t_getAbsoluteMax = a => {\n\t\tlet max = Math.max(...a),\n\t\t\tmin = Math.min(...a);\n\t\treturn Math.abs(max) >= Math.abs(min) ? max : min;\n\t},\n\t_setScrollTrigger = () => {\n\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\tScrollTrigger && ScrollTrigger.core && _integrate();\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (!_coreInitted && gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docEl = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || function() {};\n\t\t\t_pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\";\n\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t_isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : (\"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0) ? 2 : 0;\n\t\t\t_eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n\t\t\tsetTimeout(() => _startup = 0, 500);\n\t\t\t_setScrollTrigger();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t\treturn _coreInitted;\n\t};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\n\nexport class Observer {\n\tconstructor(vars) {\n\t\tthis.init(vars);\n\t}\n\n\tinit(vars) {\n\t\t_coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n\t\tScrollTrigger || _setScrollTrigger();\n\t\tlet {tolerance, dragMinimum, type, target, lineHeight, debounce, preventDefault, onStop, onStopDelay, ignore, wheelSpeed, event, onDragStart, onDragEnd, onDrag, onPress, onRelease, onRight, onLeft, onUp, onDown, onChangeX, onChangeY, onChange, onToggleX, onToggleY, onHover, onHoverEnd, onMove, ignoreCheck, isNormalizer, onGestureStart, onGestureEnd, onWheel, onEnable, onDisable, onClick, scrollSpeed, capture, allowClicks, lockAxis, onLockAxis} = vars;\n\t\tthis.target = target = _getTarget(target) || _docEl;\n\t\tthis.vars = vars;\n\t\tignore && (ignore = gsap.utils.toArray(ignore));\n\t\ttolerance = tolerance || 1e-9;\n\t\tdragMinimum = dragMinimum || 0;\n\t\twheelSpeed = wheelSpeed || 1;\n\t\tscrollSpeed = scrollSpeed || 1;\n\t\ttype = type || \"wheel,touch,pointer\";\n\t\tdebounce = debounce !== false;\n\t\tlineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\t\tlet id, onStopDelayedCall, dragged, moved, wheeled, locked, axis,\n\t\t\tself = this,\n\t\t\tprevDeltaX = 0,\n\t\t\tprevDeltaY = 0,\n\t\t\tpassive = vars.passive || !preventDefault,\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollX = scrollFuncX(),\n\t\t\tscrollY = scrollFuncY(),\n\t\t\tlimitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\", // for devices that accommodate mouse events and touch events, we need to distinguish.\n\t\t\tisViewport = _isViewport(target),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tdeltaX = [0, 0, 0], // wheel, scroll, pointer/touch\n\t\t\tdeltaY = [0, 0, 0],\n\t\t\tonClickTime = 0,\n\t\t\tclickCapture = () => onClickTime = _getTime(),\n\t\t\t_ignoreCheck = (e, isPointerOrTouch) => (self.event = e) && (ignore && ~ignore.indexOf(e.target)) || (isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\") || (ignoreCheck && ignoreCheck(e, isPointerOrTouch)),\n\t\t\tonStopFunc = () => {\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tonStop && onStop(self);\n\t\t\t},\n\t\t\tupdate = () => {\n\t\t\t\tlet dx = self.deltaX = _getAbsoluteMax(deltaX),\n\t\t\t\t\tdy = self.deltaY = _getAbsoluteMax(deltaY),\n\t\t\t\t\tchangedX = Math.abs(dx) >= tolerance,\n\t\t\t\t\tchangedY = Math.abs(dy) >= tolerance;\n\t\t\t\tonChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\t\t\t\tif (changedX) {\n\t\t\t\t\tonRight && self.deltaX > 0 && onRight(self);\n\t\t\t\t\tonLeft && self.deltaX < 0 && onLeft(self);\n\t\t\t\t\tonChangeX && onChangeX(self);\n\t\t\t\t\tonToggleX && ((self.deltaX < 0) !== (prevDeltaX < 0)) && onToggleX(self);\n\t\t\t\t\tprevDeltaX = self.deltaX;\n\t\t\t\t\tdeltaX[0] = deltaX[1] = deltaX[2] = 0\n\t\t\t\t}\n\t\t\t\tif (changedY) {\n\t\t\t\t\tonDown && self.deltaY > 0 && onDown(self);\n\t\t\t\t\tonUp && self.deltaY < 0 && onUp(self);\n\t\t\t\t\tonChangeY && onChangeY(self);\n\t\t\t\t\tonToggleY && ((self.deltaY < 0) !== (prevDeltaY < 0)) && onToggleY(self);\n\t\t\t\t\tprevDeltaY = self.deltaY;\n\t\t\t\t\tdeltaY[0] = deltaY[1] = deltaY[2] = 0\n\t\t\t\t}\n\t\t\t\tif (moved || dragged) {\n\t\t\t\t\tonMove && onMove(self);\n\t\t\t\t\tif (dragged) {\n\t\t\t\t\t\tonDrag(self);\n\t\t\t\t\t\tdragged = false;\n\t\t\t\t\t}\n\t\t\t\t\tmoved = false;\n\t\t\t\t}\n\t\t\t\tlocked && !(locked = false) && onLockAxis && onLockAxis(self);\n\t\t\t\tif (wheeled) {\n\t\t\t\t\tonWheel(self);\n\t\t\t\t\twheeled = false;\n\t\t\t\t}\n\t\t\t\tid = 0;\n\t\t\t},\n\t\t\tonDelta = (x, y, index) => {\n\t\t\t\tdeltaX[index] += x;\n\t\t\t\tdeltaY[index] += y;\n\t\t\t\tself._vx.update(x);\n\t\t\t\tself._vy.update(y);\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\tonTouchOrPointerDelta = (x, y) => {\n\t\t\t\tif (lockAxis && !axis) {\n\t\t\t\t\tself.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n\t\t\t\t\tlocked = true;\n\t\t\t\t}\n\t\t\t\tif (axis !== \"y\") {\n\t\t\t\t\tdeltaX[2] += x;\n\t\t\t\t\tself._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\t\t\t\t}\n\t\t\t\tif (axis !== \"x\") {\n\t\t\t\t\tdeltaY[2] += y;\n\t\t\t\t\tself._vy.update(y, true);\n\t\t\t\t}\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\t_onDrag = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y,\n\t\t\t\t\tisDragging = self.isDragging;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tif (isDragging || Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum) {\n\t\t\t\t\tonDrag && (dragged = true);\n\t\t\t\t\tisDragging || (self.isDragging = true);\n\t\t\t\t\tonTouchOrPointerDelta(dx, dy);\n\t\t\t\t\tisDragging || onDragStart && onDragStart(self);\n\t\t\t\t}\n\t\t\t},\n\t\t\t_onPress = self.onPress = e => {\n\t\t\t\tif (_ignoreCheck(e, 1) || (e && e.button)) {return;}\n\t\t\t\tself.axis = axis = null;\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tself.isPressed = true;\n\t\t\t\te = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\t\t\t\tprevDeltaX = prevDeltaY = 0;\n\t\t\t\tself.startX = self.x = e.clientX;\n\t\t\t\tself.startY = self.y = e.clientY;\n\t\t\t\tself._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\t\t\t\tself._vy.reset();\n\t\t\t\t_addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, passive, true);\n\t\t\t\tself.deltaX = self.deltaY = 0;\n\t\t\t\tonPress && onPress(self);\n\t\t\t},\n\t\t\t_onRelease = self.onRelease = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\tlet isTrackingDrag = !isNaN(self.y - self.startY),\n\t\t\t\t\twasDragging = self.isDragging,\n\t\t\t\t\tisDragNotClick = wasDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3), // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n\t\t\t\t\teventData = _getEvent(e);\n\t\t\t\tif (!isDragNotClick && isTrackingDrag) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t//if (preventDefault && allowClicks && self.isPressed) { // check isPressed because in a rare edge case, the inputObserver in ScrollTrigger may stopPropagation() on the press/drag, so the onRelease may get fired without the onPress/onDrag ever getting called, thus it could trigger a click to occur on a link after scroll-dragging it.\n\t\t\t\t\tif (preventDefault && allowClicks) {\n\t\t\t\t\t\tgsap.delayedCall(0.08, () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (e.target.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\te.target.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tlet syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\te.target.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t\t\tonStop && wasDragging && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t\tonDragEnd && wasDragging && onDragEnd(self);\n\t\t\t\tonRelease && onRelease(self, isDragNotClick);\n\t\t\t},\n\t\t\t_onGestureStart = e => e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging),\n\t\t\t_onGestureEnd = () => (self.isGesturing = false) || onGestureEnd(self),\n\t\t\tonScroll = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = scrollFuncX(),\n\t\t\t\t\ty = scrollFuncY();\n\t\t\t\tonDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n\t\t\t\tscrollX = x;\n\t\t\t\tscrollY = y;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onWheel = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tonWheel && (wheeled = true);\n\t\t\t\tlet multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n\t\t\t\tonDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onMove = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tmoved = true;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t\t(dx || dy) && onTouchOrPointerDelta(dx, dy);\n\t\t\t},\n\t\t\t_onHover = e => {self.event = e; onHover(self);},\n\t\t\t_onHoverEnd = e => {self.event = e; onHoverEnd(self);},\n\t\t\t_onClick = e => _ignoreCheck(e) || (_getEvent(e, preventDefault) && onClick(self));\n\n\t\tonStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n\n\t\tself.deltaX = self.deltaY = 0;\n\t\tself._vx = _getVelocityProp(0, 50, true);\n\t\tself._vy = _getVelocityProp(0, 50, true);\n\t\tself.scrollX = scrollFuncX;\n\t\tself.scrollY = scrollFuncY;\n\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t_context(this);\n\t\tself.enable = e => {\n\t\t\tif (!self.isEnabled) {\n\t\t\t\t_addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\ttype.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, passive, capture);\n\t\t\t\ttype.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, passive, capture);\n\t\t\t\tif ((type.indexOf(\"touch\") >= 0 && _isTouch) || type.indexOf(\"pointer\") >= 0) {\n\t\t\t\t\t_addListener(target, _eventTypes[0], _onPress, passive, capture);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t\tallowClicks && _addListener(target, \"click\", clickCapture, true, true);\n\t\t\t\t\tonClick && _addListener(target, \"click\", _onClick);\n\t\t\t\t\tonGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t\tonGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t\tonHover && _addListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t\tonHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t\tonMove && _addListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\t}\n\t\t\t\tself.isEnabled = true;\n\t\t\t\te && e.type && _onPress(e);\n\t\t\t\tonEnable && onEnable(self);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\t\tself.disable = () => {\n\t\t\tif (self.isEnabled) {\n\t\t\t\t// only remove the _onScroll listener if there aren't any others that rely on the functionality.\n\t\t\t\t_observers.filter(o => o !== self && _isViewport(o.target)).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\tif (self.isPressed) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\t}\n\t\t\t\t_removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\t\t\t\t_removeListener(target, \"wheel\", _onWheel, capture);\n\t\t\t\t_removeListener(target, _eventTypes[0], _onPress, capture);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t_removeListener(target, \"click\", clickCapture, true);\n\t\t\t\t_removeListener(target, \"click\", _onClick);\n\t\t\t\t_removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t_removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t_removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\tself.isEnabled = self.isPressed = self.isDragging = false;\n\t\t\t\tonDisable && onDisable(self);\n\t\t\t}\n\t\t};\n\n\t\tself.kill = self.revert = () => {\n\t\t\tself.disable();\n\t\t\tlet i = _observers.indexOf(self);\n\t\t\ti >= 0 && _observers.splice(i, 1);\n\t\t\t_normalizer === self && (_normalizer = 0);\n\t\t}\n\n\t\t_observers.push(self);\n\t\tisNormalizer && _isViewport(target) && (_normalizer = self);\n\n\t\tself.enable(event);\n\t}\n\n\tget velocityX() {\n\t\treturn this._vx.getVelocity();\n\t}\n\tget velocityY() {\n\t\treturn this._vy.getVelocity();\n\t}\n\n}\n\nObserver.version = \"3.12.5\";\nObserver.create = vars => new Observer(vars);\nObserver.register = _initCore;\nObserver.getAll = () => _observers.slice();\nObserver.getById = id => _observers.filter(o => o.vars.id === id)[0];\n\n_getGSAP() && gsap.registerPlugin(Observer);\n\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };", "/*!\n * ScrollTrigger 3.12.5\n * https://gsap.com\n *\n * @license Copyright 2008-2024, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license or for\n * Club GSAP members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { Observer, _getTarget, _vertical, _horizontal, _scrollers, _proxies, _getScrollFunc, _getProxyProp, _getVelocityProp } from \"./Observer.js\";\n\nlet gsap, _coreInitted, _win, _doc, _docEl, _body, _root, _resizeDelay, _toArray, _clamp, _time2, _syncInterval, _refreshing, _pointerIsDown, _transformProp, _i, _prevWidth, _prevHeight, _autoRefresh, _sort, _suppressOverwrites, _ignoreResize, _normalizer, _ignoreMobileResize, _baseScreenHeight, _baseScreenWidth, _fixIOSBug, _context, _scrollRestoration, _div100vh, _100vh, _isReverted, _clampingMax,\n\t_limitCallbacks, // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n\t_startup = 1,\n\t_getTime = Date.now,\n\t_time1 = _getTime(),\n\t_lastScrollTime = 0,\n\t_enabled = 0,\n\t_parseClamp = (value, type, self) => {\n\t\tlet clamp = (_isString(value) && (value.substr(0, 6) === \"clamp(\" || value.indexOf(\"max\") > -1));\n\t\tself[\"_\" + type + \"Clamp\"] = clamp;\n\t\treturn clamp ? value.substr(6, value.length - 7) : value;\n\t},\n\t_keepClamp = (value, clamp) => clamp && (!_isString(value) || value.substr(0, 6) !== \"clamp(\") ? \"clamp(\" + value + \")\" : value,\n\t_rafBugFix = () => _enabled && requestAnimationFrame(_rafBugFix), // in some browsers (like Firefox), screen repaints weren't consistent unless we had SOMETHING queued up in requestAnimationFrame()! So this just creates a super simple loop to keep it alive and smooth out repaints.\n\t_pointerDownHandler = () => _pointerIsDown = 1,\n\t_pointerUpHandler = () => _pointerIsDown = 0,\n\t_passThrough = v => v,\n\t_round = value => Math.round(value * 100000) / 100000 || 0,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isViewport = e => !!~_root.indexOf(e),\n\t_getViewportDimension = dimensionProperty => (dimensionProperty === \"Height\" ? _100vh : _win[\"inner\" + dimensionProperty]) || _docEl[\"client\" + dimensionProperty] || _body[\"client\" + dimensionProperty],\n\t_getBoundsFunc = element => _getProxyProp(element, \"getBoundingClientRect\") || (_isViewport(element) ? () => {_winOffsets.width = _win.innerWidth; _winOffsets.height = _100vh; return _winOffsets;} : () => _getBounds(element)),\n\t_getSizeFunc = (scroller, isViewport, {d, d2, a}) => (a = _getProxyProp(scroller, \"getBoundingClientRect\")) ? () => a()[d] : () => (isViewport ? _getViewportDimension(d2) : scroller[\"client\" + d2]) || 0,\n\t_getOffsetsFunc = (element, isViewport) => !isViewport || ~_proxies.indexOf(element) ? _getBoundsFunc(element) : () => _winOffsets,\n\t_maxScroll = (element, {s, d2, d, a}) => Math.max(0, (s = \"scroll\" + d2) && (a = _getProxyProp(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - _getViewportDimension(d2) : element[s] - element[\"offset\" + d2]),\n\t_iterateAutoRefresh = (func, events) => {\n\t\tfor (let i = 0; i < _autoRefresh.length; i += 3) {\n\t\t\t(!events || ~events.indexOf(_autoRefresh[i+1])) && func(_autoRefresh[i], _autoRefresh[i+1], _autoRefresh[i+2]);\n\t\t}\n\t},\n\t_isString = value => typeof(value) === \"string\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_endAnimation = (animation, reversed, pause) => animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause(),\n\t_callback = (self, func) => {\n\t\tif (self.enabled) {\n\t\t\tlet result = self._ctx ? self._ctx.add(() => func(self)) : func(self);\n\t\t\tresult && result.totalTime && (self.callbackAnimation = result);\n\t\t}\n\t},\n\t_abs = Math.abs,\n\t_left = \"left\",\n\t_top = \"top\",\n\t_right = \"right\",\n\t_bottom = \"bottom\",\n\t_width = \"width\",\n\t_height = \"height\",\n\t_Right = \"Right\",\n\t_Left = \"Left\",\n\t_Top = \"Top\",\n\t_Bottom = \"Bottom\",\n\t_padding = \"padding\",\n\t_margin = \"margin\",\n\t_Width = \"Width\",\n\t_Height = \"Height\",\n\t_px = \"px\",\n\t_getComputedStyle = element => _win.getComputedStyle(element),\n\t_makePositionable = element => { // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n\t\tlet position = _getComputedStyle(element).position;\n\t\telement.style.position = (position === \"absolute\" || position === \"fixed\") ? position : \"relative\";\n\t},\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\t(p in obj) || (obj[p] = defaults[p]);\n\t\t}\n\t\treturn obj;\n\t},\n\t_getBounds = (element, withoutTransforms) => {\n\t\tlet tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {x: 0, y: 0, xPercent: 0, yPercent: 0, rotation: 0, rotationX: 0, rotationY: 0, scale: 1, skewX: 0, skewY: 0}).progress(1),\n\t\t\tbounds = element.getBoundingClientRect();\n\t\ttween && tween.progress(0).kill();\n\t\treturn bounds;\n\t},\n\t_getSize = (element, {d2}) => element[\"offset\" + d2] || element[\"client\" + d2] || 0,\n\t_getLabelRatioArray = timeline => {\n\t\tlet a = [],\n\t\t\tlabels = timeline.labels,\n\t\t\tduration = timeline.duration(),\n\t\t\tp;\n\t\tfor (p in labels) {\n\t\t\ta.push(labels[p] / duration);\n\t\t}\n\t\treturn a;\n\t},\n\t_getClosestLabel = animation => value => gsap.utils.snap(_getLabelRatioArray(animation), value),\n\t_snapDirectional = snapIncrementOrArray => {\n\t\tlet snap = gsap.utils.snap(snapIncrementOrArray),\n\t\t\ta = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort((a, b) => a - b);\n\t\treturn a ? (value, direction, threshold= 1e-3) => {\n\t\t\tlet i;\n\t\t\tif (!direction) {\n\t\t\t\treturn snap(value);\n\t\t\t}\n\t\t\tif (direction > 0) {\n\t\t\t\tvalue -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\t\t\t\tfor (i = 0; i < a.length; i++) {\n\t\t\t\t\tif (a[i] >= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn a[i-1];\n\t\t\t} else {\n\t\t\t\ti = a.length;\n\t\t\t\tvalue += threshold;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tif (a[i] <= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn a[0];\n\t\t} : (value, direction, threshold= 1e-3) => {\n\t\t\tlet snapped = snap(value);\n\t\t\treturn !direction || Math.abs(snapped - value) < threshold || ((snapped - value < 0) === direction < 0) ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n\t\t};\n\t},\n\t_getLabelAtDirection = timeline => (value, st) => _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction),\n\t_multiListener = (func, element, types, callback) => types.split(\",\").forEach(type => func(element, type, callback)),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_wheelListener = (func, el, scrollFunc) => {\n\t\tscrollFunc = scrollFunc && scrollFunc.wheelHandler\n\t\tif (scrollFunc) {\n\t\t\tfunc(el, \"wheel\", scrollFunc);\n\t\t\tfunc(el, \"touchmove\", scrollFunc);\n\t\t}\n\t},\n\t_markerDefaults = {startColor: \"green\", endColor: \"red\", indent: 0, fontSize: \"16px\", fontWeight:\"normal\"},\n\t_defaults = {toggleActions: \"play\", anticipatePin: 0},\n\t_keywords = {top: 0, left: 0, center: 0.5, bottom: 1, right: 1},\n\t_offsetToPx = (value, size) => {\n\t\tif (_isString(value)) {\n\t\t\tlet eqIndex = value.indexOf(\"=\"),\n\t\t\t\trelative = ~eqIndex ? +(value.charAt(eqIndex-1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\t\t\tif (~eqIndex) {\n\t\t\t\t(value.indexOf(\"%\") > eqIndex) && (relative *= size / 100);\n\t\t\t\tvalue = value.substr(0, eqIndex-1);\n\t\t\t}\n\t\t\tvalue = relative + ((value in _keywords) ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n\t\t}\n\t\treturn value;\n\t},\n\t_createMarker = (type, name, container, direction, {startColor, endColor, fontSize, indent, fontWeight}, offset, matchWidthEl, containerAnimation) => {\n\t\tlet e = _doc.createElement(\"div\"),\n\t\t\tuseFixedPosition = _isViewport(container) || _getProxyProp(container, \"pinType\") === \"fixed\",\n\t\t\tisScroller = type.indexOf(\"scroller\") !== -1,\n\t\t\tparent = useFixedPosition ? _body : container,\n\t\t\tisStart = type.indexOf(\"start\") !== -1,\n\t\t\tcolor = isStart ? startColor : endColor,\n\t\t\tcss = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\t\tcss += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n\t\t(isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n\t\tmatchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n\t\te._isStart = isStart;\n\t\te.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n\t\te.style.cssText = css;\n\t\te.innerText = name || name === 0 ? type + \"-\" + name : type;\n\t\tparent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n\t\te._offset = e[\"offset\" + direction.op.d2];\n\t\t_positionMarker(e, 0, direction, isStart);\n\t\treturn e;\n\t},\n\t_positionMarker = (marker, start, direction, flipped) => {\n\t\tlet vars = {display: \"block\"},\n\t\t\tside = direction[flipped ? \"os2\" : \"p2\"],\n\t\t\toppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n\t\tmarker._isFlipped = flipped;\n\t\tvars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n\t\tvars[direction.a] = flipped ? \"1px\" : 0;\n\t\tvars[\"border\" + side + _Width] = 1;\n\t\tvars[\"border\" + oppositeSide + _Width] = 0;\n\t\tvars[direction.p] = start + \"px\";\n\t\tgsap.set(marker, vars);\n\t},\n\t_triggers = [],\n\t_ids = {},\n\t_rafID,\n\t_sync = () => _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll))),\n\t_onScroll = () => { // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n\t\tif (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) { // if the user is dragging the scrollbar, allow it.\n\t\t\t_scrollers.cache++;\n\t\t\tif (_normalizer) {\n\t\t\t\t_rafID || (_rafID = requestAnimationFrame(_updateAll));\n\t\t\t} else {\n\t\t\t\t_updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\t\t\t}\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t}\n\t},\n\t_setBaseDimensions = () => {\n\t\t_baseScreenWidth = _win.innerWidth;\n\t\t_baseScreenHeight = _win.innerHeight;\n\t},\n\t_onResize = () => {\n\t\t_scrollers.cache++;\n\t\t!_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25) && _resizeDelay.restart(true);\n\t}, // ignore resizes triggered by refresh()\n\t_listeners = {},\n\t_emptyArray = [],\n\t_softRefresh = () => _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true),\n\t_dispatch = type => (_listeners[type] && _listeners[type].map(f => f())) || _emptyArray,\n\t_savedStyles = [], // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n\t_revertRecorded = media => {\n\t\tfor (let i = 0; i < _savedStyles.length; i+=5) {\n\t\t\tif (!media || _savedStyles[i+4] && _savedStyles[i+4].query === media) {\n\t\t\t\t_savedStyles[i].style.cssText = _savedStyles[i+1];\n\t\t\t\t_savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i+2] || \"\");\n\t\t\t\t_savedStyles[i+3].uncache = 1;\n\t\t\t}\n\t\t}\n\t},\n\t_revertAll = (kill, media) => {\n\t\tlet trigger;\n\t\tfor (_i = 0; _i < _triggers.length; _i++) {\n\t\t\ttrigger = _triggers[_i];\n\t\t\tif (trigger && (!media || trigger._ctx === media)) {\n\t\t\t\tif (kill) {\n\t\t\t\t\ttrigger.kill(1);\n\t\t\t\t} else {\n\t\t\t\t\ttrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_isReverted = true;\n\t\tmedia && _revertRecorded(media);\n\t\tmedia || _dispatch(\"revert\");\n\t},\n\t_clearScrollMemory = (scrollRestoration, force) => { // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n\t\t_scrollers.cache++;\n\t\t(force || !_refreshingAll) && _scrollers.forEach(obj => _isFunction(obj) && obj.cacheID++ && (obj.rec = 0));\n\t\t_isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n\t},\n\t_refreshingAll,\n\t_refreshID = 0,\n\t_queueRefreshID,\n\t_queueRefreshAll = () => { // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n\t\tif (_queueRefreshID !== _refreshID) {\n\t\t\tlet id = _queueRefreshID = _refreshID;\n\t\t\trequestAnimationFrame(() => id === _refreshID && _refreshAll(true));\n\t\t}\n\t},\n\t_refresh100vh = () => {\n\t\t_body.appendChild(_div100vh);\n\t\t_100vh = (!_normalizer && _div100vh.offsetHeight) || _win.innerHeight;\n\t\t_body.removeChild(_div100vh);\n\t},\n\t_hideAllMarkers = hide => _toArray(\".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end\").forEach(el => el.style.display = hide ? \"none\" : \"block\"),\n\t_refreshAll = (force, skipRevert) => {\n\t\tif (_lastScrollTime && !force && !_isReverted) {\n\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\treturn;\n\t\t}\n\t\t_refresh100vh();\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = true;\n\t\t_scrollers.forEach(obj => _isFunction(obj) && ++obj.cacheID && (obj.rec = obj())); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\t\tlet refreshInits = _dispatch(\"refreshInit\");\n\t\t_sort && ScrollTrigger.sort();\n\t\tskipRevert || _revertAll();\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\t\t\t\tobj(0);\n\t\t\t}\n\t\t});\n\t\t_triggers.slice(0).forEach(t => t.refresh()) // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\t\t_isReverted = false;\n\t\t_triggers.forEach((t) => { // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n\t\t\tif (t._subPinOffset && t.pin) {\n\t\t\t\tlet prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n\t\t\t\t\toriginal = t.pin[prop];\n\t\t\t\tt.revert(true, 1);\n\t\t\t\tt.adjustPinSpacing(t.pin[prop] - original);\n\t\t\t\tt.refresh();\n\t\t\t}\n\t\t});\n\t\t_clampingMax = 1; // pinSpacing might be propping a page open, thus when we .setPositions() to clamp a ScrollTrigger's end we should leave the pinSpacing alone. That's what this flag is for.\n\t\t_hideAllMarkers(true);\n\t\t_triggers.forEach(t => { // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\". Same for anything with a clamped end\n\t\t\tlet max = _maxScroll(t.scroller, t._dir),\n\t\t\t\tendClamp = t.vars.end === \"max\" || (t._endClamp && t.end > max),\n\t\t\t\tstartClamp = t._startClamp && t.start >= max;\n\t\t\t(endClamp || startClamp) && t.setPositions(startClamp ? max - 1 : t.start, endClamp ? Math.max(startClamp ? max : t.start + 1, max) : t.end, true);\n\t\t});\n\t\t_hideAllMarkers(false);\n\t\t_clampingMax = 0;\n\t\trefreshInits.forEach(result => result && result.render && result.render(-1)); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && requestAnimationFrame(() => obj.target.style.scrollBehavior = \"smooth\");\n\t\t\t\tobj.rec && obj(obj.rec);\n\t\t\t}\n\t\t});\n\t\t_clearScrollMemory(_scrollRestoration, 1);\n\t\t_resizeDelay.pause();\n\t\t_refreshID++;\n\t\t_refreshingAll = 2;\n\t\t_updateAll(2);\n\t\t_triggers.forEach(t => _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t));\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = false;\n\t\t_dispatch(\"refresh\");\n\t},\n\t_lastScroll = 0,\n\t_direction = 1,\n\t_primary,\n\t_updateAll = (force) => {\n\t\tif (force === 2 || (!_refreshingAll && !_isReverted)) { // _isReverted could be true if, for example, a matchMedia() is in the process of executing. We don't want to update during the time everything is reverted.\n\t\t\tScrollTrigger.isUpdating = true;\n\t\t\t_primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\t\t\tlet l = _triggers.length,\n\t\t\t\ttime = _getTime(),\n\t\t\t\trecordVelocity = time - _time1 >= 50,\n\t\t\t\tscroll = l && _triggers[0].scroll();\n\t\t\t_direction = _lastScroll > scroll ? -1 : 1;\n\t\t\t_refreshingAll || (_lastScroll = scroll);\n\t\t\tif (recordVelocity) {\n\t\t\t\tif (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n\t\t\t\t\t_lastScrollTime = 0;\n\t\t\t\t\t_dispatch(\"scrollEnd\");\n\t\t\t\t}\n\t\t\t\t_time2 = _time1;\n\t\t\t\t_time1 = time;\n\t\t\t}\n\t\t\tif (_direction < 0) {\n\t\t\t\t_i = l;\n\t\t\t\twhile (_i-- > 0) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t\t_direction = 1;\n\t\t\t} else {\n\t\t\t\tfor (_i = 0; _i < l; _i++) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t}\n\t\t\tScrollTrigger.isUpdating = false;\n\t\t}\n\t\t_rafID = 0;\n\t},\n\t_propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n\t_stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n\t_swapPinOut = (pin, spacer, state) => {\n\t\t_setState(state);\n\t\tlet cache = pin._gsap;\n\t\tif (cache.spacerIsNative) {\n\t\t\t_setState(cache.spacerState);\n\t\t} else if (pin._gsap.swappedIn) {\n\t\t\tlet parent = spacer.parentNode;\n\t\t\tif (parent) {\n\t\t\t\tparent.insertBefore(pin, spacer);\n\t\t\t\tparent.removeChild(spacer);\n\t\t\t}\n\t\t}\n\t\tpin._gsap.swappedIn = false;\n\t},\n\t_swapPinIn = (pin, spacer, cs, spacerState) => {\n\t\tif (!pin._gsap.swappedIn) {\n\t\t\tlet i = _propNamesToCopy.length,\n\t\t\t\tspacerStyle = spacer.style,\n\t\t\t\tpinStyle = pin.style,\n\t\t\t\tp;\n\t\t\twhile (i--) {\n\t\t\t\tp = _propNamesToCopy[i];\n\t\t\t\tspacerStyle[p] = cs[p];\n\t\t\t}\n\t\t\tspacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n\t\t\t(cs.display === \"inline\") && (spacerStyle.display = \"inline-block\");\n\t\t\tpinStyle[_bottom] = pinStyle[_right] = \"auto\";\n\t\t\tspacerStyle.flexBasis = cs.flexBasis || \"auto\";\n\t\t\tspacerStyle.overflow = \"visible\";\n\t\t\tspacerStyle.boxSizing = \"border-box\";\n\t\t\tspacerStyle[_width] = _getSize(pin, _horizontal) + _px;\n\t\t\tspacerStyle[_height] = _getSize(pin, _vertical) + _px;\n\t\t\tspacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\t\t\t_setState(spacerState);\n\t\t\tpinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n\t\t\tpinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n\t\t\tpinStyle[_padding] = cs[_padding];\n\t\t\tif (pin.parentNode !== spacer) {\n\t\t\t\tpin.parentNode.insertBefore(spacer, pin);\n\t\t\t\tspacer.appendChild(pin);\n\t\t\t}\n\t\t\tpin._gsap.swappedIn = true;\n\t\t}\n\t},\n\t_capsExp = /([A-Z])/g,\n\t_setState = state => {\n\t\tif (state) {\n\t\t\tlet style = state.t.style,\n\t\t\t\tl = state.length,\n\t\t\t\ti = 0,\n\t\t\t\tp, value;\n\t\t\t(state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\t\t\tfor (; i < l; i +=2) {\n\t\t\t\tvalue = state[i+1];\n\t\t\t\tp = state[i];\n\t\t\t\tif (value) {\n\t\t\t\t\tstyle[p] = value;\n\t\t\t\t} else if (style[p]) {\n\t\t\t\t\tstyle.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t_getState = element => { // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n\t\tlet l = _stateProps.length,\n\t\t\tstyle = element.style,\n\t\t\tstate = [],\n\t\t\ti = 0;\n\t\tfor (; i < l; i++) {\n\t\t\tstate.push(_stateProps[i], style[_stateProps[i]]);\n\t\t}\n\t\tstate.t = element;\n\t\treturn state;\n\t},\n\t_copyState = (state, override, omitOffsets) => {\n\t\tlet result = [],\n\t\t\tl = state.length,\n\t\t\ti = omitOffsets ? 8 : 0, // skip top, left, right, bottom if omitOffsets is true\n\t\t\tp;\n\t\tfor (; i < l; i += 2) {\n\t\t\tp = state[i];\n\t\t\tresult.push(p, (p in override) ? override[p] : state[i+1]);\n\t\t}\n\t\tresult.t = state.t;\n\t\treturn result;\n\t},\n\t_winOffsets = {left:0, top:0},\n\t// // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n\t// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n\t// \tscroller = _getTarget(scroller || _win);\n\t// \tlet direction = horizontal ? _horizontal : _vertical,\n\t// \t\tisViewport = _isViewport(scroller);\n\t// \t_getSizeFunc(scroller, isViewport, direction);\n\t// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n\t// },\n\t_parsePosition = (value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation, clampZeroProp) => {\n\t\t_isFunction(value) && (value = value(self));\n\t\tif (_isString(value) && value.substr(0,3) === \"max\") {\n\t\t\tvalue = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n\t\t}\n\t\tlet time = containerAnimation ? containerAnimation.time() : 0,\n\t\t\tp1, p2, element;\n\t\tcontainerAnimation && containerAnimation.seek(0);\n\t\tisNaN(value) || (value = +value); // convert a string number like \"45\" to an actual number\n\t\tif (!_isNumber(value)) {\n\t\t\t_isFunction(trigger) && (trigger = trigger(self));\n\t\t\tlet offsets = (value || \"0\").split(\" \"),\n\t\t\t\tbounds, localOffset, globalOffset, display;\n\t\t\telement = _getTarget(trigger, self) || _body;\n\t\t\tbounds = _getBounds(element) || {};\n\t\t\tif ((!bounds || (!bounds.left && !bounds.top)) && _getComputedStyle(element).display === \"none\") { // if display is \"none\", it won't report getBoundingClientRect() properly\n\t\t\t\tdisplay = element.style.display;\n\t\t\t\telement.style.display = \"block\";\n\t\t\t\tbounds = _getBounds(element);\n\t\t\t\tdisplay ? (element.style.display = display) : element.style.removeProperty(\"display\");\n\t\t\t}\n\t\t\tlocalOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n\t\t\tglobalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n\t\t\tvalue = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n\t\t\tmarkerScroller && _positionMarker(markerScroller, globalOffset, direction, (scrollerSize - globalOffset < 20 || (markerScroller._isStart && globalOffset > 20)));\n\t\t\tscrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n\t\t} else {\n\t\t\tcontainerAnimation && (value = gsap.utils.mapRange(containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, 0, scrollerMax, value));\n\t\t\tmarkerScroller && _positionMarker(markerScroller, scrollerSize, direction, true);\n\t\t}\n\t\tif (clampZeroProp) {\n\t\t\tself[clampZeroProp] = value || -0.001;\n\t\t\tvalue < 0 && (value = 0);\n\t\t}\n\t\tif (marker) {\n\t\t\tlet position = value + scrollerSize,\n\t\t\t\tisStart = marker._isStart;\n\t\t\tp1 = \"scroll\" + direction.d2;\n\t\t\t_positionMarker(marker, position, direction, (isStart && position > 20) || (!isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1));\n\t\t\tif (useFixedPosition) {\n\t\t\t\tscrollerBounds = _getBounds(markerScroller);\n\t\t\t\tuseFixedPosition && (marker.style[direction.op.p] = (scrollerBounds[direction.op.p] - direction.op.m - marker._offset) + _px);\n\t\t\t}\n\t\t}\n\t\tif (containerAnimation && element) {\n\t\t\tp1 = _getBounds(element);\n\t\t\tcontainerAnimation.seek(scrollerMax);\n\t\t\tp2 = _getBounds(element);\n\t\t\tcontainerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n\t\t\tvalue = value / (containerAnimation._caScrollDist) * scrollerMax;\n\t\t}\n\t\tcontainerAnimation && containerAnimation.seek(time);\n\t\treturn containerAnimation ? value : Math.round(value);\n\t},\n\t_prefixExp = /(webkit|moz|length|cssText|inset)/i,\n\t_reparent = (element, parent, top, left) => {\n\t\tif (element.parentNode !== parent) {\n\t\t\tlet style = element.style,\n\t\t\t\tp, cs;\n\t\t\tif (parent === _body) {\n\t\t\t\telement._stOrig = style.cssText; // record original inline styles so we can revert them later\n\t\t\t\tcs = _getComputedStyle(element);\n\t\t\t\tfor (p in cs) { // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n\t\t\t\t\tif (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n\t\t\t\t\t\tstyle[p] = cs[p];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstyle.top = top;\n\t\t\t\tstyle.left = left;\n\t\t\t} else {\n\t\t\t\tstyle.cssText = element._stOrig;\n\t\t\t}\n\t\t\tgsap.core.getCache(element).uncache = 1;\n\t\t\tparent.appendChild(element);\n\t\t}\n\t},\n\t_interruptionTracker = (getValueFunc, initialValue, onInterrupt) => {\n\t\tlet last1 = initialValue,\n\t\t\tlast2 = last1;\n\t\treturn value => {\n\t\t\tlet current = Math.round(getValueFunc()); // round because in some [very uncommon] Windows environments, scroll can get reported with decimals even though it was set without.\n\t\t\tif (current !== last1 && current !== last2 && Math.abs(current - last1) > 3 && Math.abs(current - last2) > 3) { // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n\t\t\t\tvalue = current;\n\t\t\t\tonInterrupt && onInterrupt();\n\t\t\t}\n\t\t\tlast2 = last1;\n\t\t\tlast1 = value;\n\t\t\treturn value;\n\t\t};\n\t},\n\t_shiftMarker = (marker, direction, value) => {\n\t\tlet vars = {};\n\t\tvars[direction.p] = \"+=\" + value;\n\t\tgsap.set(marker, vars);\n\t},\n\t// _mergeAnimations = animations => {\n\t// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n\t// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n\t// \ttl.smoothChildTiming = false;\n\t// \treturn tl;\n\t// },\n\n\t// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n\t_getTweenCreator = (scroller, direction) => {\n\t\tlet getScroll = _getScrollFunc(scroller, direction),\n\t\t\tprop = \"_scroll\" + direction.p2, // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n\t\t\tgetTween = (scrollTo, vars, initialValue, change1, change2) => {\n\t\t\t\tlet tween = getTween.tween,\n\t\t\t\t\tonComplete = vars.onComplete,\n\t\t\t\t\tmodifiers = {};\n\t\t\t\tinitialValue = initialValue || getScroll();\n\t\t\t\tlet checkForInterruption = _interruptionTracker(getScroll, initialValue, () => {\n\t\t\t\t\ttween.kill();\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t})\n\t\t\t\tchange2 = (change1 && change2) || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\t\t\t\tchange1 = change1 || (scrollTo - initialValue);\n\t\t\t\ttween && tween.kill();\n\t\t\t\tvars[prop] = scrollTo;\n\t\t\t\tvars.inherit = false;\n\t\t\t\tvars.modifiers = modifiers;\n\t\t\t\tmodifiers[prop] = () => checkForInterruption(initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio);\n\t\t\t\tvars.onUpdate = () => {\n\t\t\t\t\t_scrollers.cache++;\n\t\t\t\t\tgetTween.tween && _updateAll(); // if it was interrupted/killed, like in a context.revert(), don't force an updateAll()\n\t\t\t\t};\n\t\t\t\tvars.onComplete = () => {\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t\tonComplete && onComplete.call(tween);\n\t\t\t\t};\n\t\t\t\ttween = getTween.tween = gsap.to(scroller, vars);\n\t\t\t\treturn tween;\n\t\t\t};\n\t\tscroller[prop] = getScroll;\n\t\tgetScroll.wheelHandler = () => getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n\t\t_addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\t\tScrollTrigger.isTouch && _addListener(scroller, \"touchmove\", getScroll.wheelHandler);\n\t\treturn getTween;\n\t};\n\n\n\n\nexport class ScrollTrigger {\n\n\tconstructor(vars, animation) {\n\t\t_coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\t\t_context(this);\n\t\tthis.init(vars, animation);\n\t}\n\n\tinit(vars, animation) {\n\t\tthis.progress = this.start = 0;\n\t\tthis.vars && this.kill(true, true); // in case it's being initted again\n\t\tif (!_enabled) {\n\t\t\tthis.update = this.refresh = this.kill = _passThrough;\n\t\t\treturn;\n\t\t}\n\t\tvars = _setDefaults((_isString(vars) || _isNumber(vars) || vars.nodeType) ? {trigger: vars} : vars, _defaults);\n\t\tlet {onUpdate, toggleClass, id, onToggle, onRefresh, scrub, trigger, pin, pinSpacing, invalidateOnRefresh, anticipatePin, onScrubComplete, onSnapComplete, once, snap, pinReparent, pinSpacer, containerAnimation, fastScrollEnd, preventOverlaps} = vars,\n\t\t\tdirection = vars.horizontal || (vars.containerAnimation && vars.horizontal !== false) ? _horizontal : _vertical,\n\t\t\tisToggle = !scrub && scrub !== 0,\n\t\t\tscroller = _getTarget(vars.scroller || _win),\n\t\t\tscrollerCache = gsap.core.getCache(scroller),\n\t\t\tisViewport = _isViewport(scroller),\n\t\t\tuseFixedPosition = (\"pinType\" in vars ? vars.pinType : _getProxyProp(scroller, \"pinType\") || (isViewport && \"fixed\")) === \"fixed\",\n\t\t\tcallbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n\t\t\ttoggleActions = isToggle && vars.toggleActions.split(\" \"),\n\t\t\tmarkers = \"markers\" in vars ? vars.markers : _defaults.markers,\n\t\t\tborderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n\t\t\tself = this,\n\t\t\tonRefreshInit = vars.onRefreshInit && (() => vars.onRefreshInit(self)),\n\t\t\tgetScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n\t\t\tgetScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n\t\t\tlastSnap = 0,\n\t\t\tlastRefresh = 0,\n\t\t\tprevProgress = 0,\n\t\t\tscrollFunc = _getScrollFunc(scroller, direction),\n\t\t\ttweenTo, pinCache, snapFunc, scroll1, scroll2, start, end, markerStart, markerEnd, markerStartTrigger, markerEndTrigger, markerVars, executingOnRefresh,\n\t\t\tchange, pinOriginalState, pinActiveState, pinState, spacer, offset, pinGetter, pinSetter, pinStart, pinChange, spacingStart, spacerState, markerStartSetter, pinMoves,\n\t\t\tmarkerEndSetter, cs, snap1, snap2, scrubTween, scrubSmooth, snapDurClamp, snapDelayedCall, prevScroll, prevAnimProgress, caMarkerSetter, customRevertReturn;\n\n\t\t// for the sake of efficiency, _startClamp/_endClamp serve like a truthy value indicating that clamping was enabled on the start/end, and ALSO store the actual pre-clamped numeric value. We tap into that in ScrollSmoother for speed effects. So for example, if start=\"clamp(top bottom)\" results in a start of -100 naturally, it would get clamped to 0 but -100 would be stored in _startClamp.\n\t\tself._startClamp = self._endClamp = false;\n\t\tself._dir = direction;\n\t\tanticipatePin *= 45;\n\t\tself.scroller = scroller;\n\t\tself.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n\t\tscroll1 = scrollFunc();\n\t\tself.vars = vars;\n\t\tanimation = animation || vars.animation;\n\t\tif (\"refreshPriority\" in vars) {\n\t\t\t_sort = 1;\n\t\t\tvars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n\t\t}\n\t\tscrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n\t\t\ttop: _getTweenCreator(scroller, _vertical),\n\t\t\tleft: _getTweenCreator(scroller, _horizontal)\n\t\t};\n\t\tself.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\t\tself.scrubDuration = value => {\n\t\t\tscrubSmooth = _isNumber(value) && value;\n\t\t\tif (!scrubSmooth) {\n\t\t\t\tscrubTween && scrubTween.progress(1).kill();\n\t\t\t\tscrubTween = 0;\n\t\t\t} else {\n\t\t\t\tscrubTween ? scrubTween.duration(value) : (scrubTween = gsap.to(animation, {ease: \"expo\", totalProgress: \"+=0\", inherit: false, duration: scrubSmooth, paused: true, onComplete: () => onScrubComplete && onScrubComplete(self)}));\n\t\t\t}\n\t\t};\n\t\tif (animation) {\n\t\t\tanimation.vars.lazy = false;\n\t\t\t(animation._initted && !self.isReverted) || (animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true)); // special case: if this ScrollTrigger gets re-initted, a from() tween with a stagger could get initted initially and then reverted on the re-init which means it'll need to get rendered again here to properly display things. Otherwise, See https://gsap.com/forums/topic/36777-scrollsmoother-splittext-nextjs/ and https://codepen.io/GreenSock/pen/eYPyPpd?editors=0010\n\t\t\tself.animation = animation.pause();\n\t\t\tanimation.scrollTrigger = self;\n\t\t\tself.scrubDuration(scrub);\n\t\t\tsnap1 = 0;\n\t\t\tid || (id = animation.vars.id);\n\t\t}\n\n\t\tif (snap) {\n\t\t\t// TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n\t\t\tif (!_isObject(snap) || snap.push) {\n\t\t\t\tsnap = {snapTo: snap};\n\t\t\t}\n\t\t\t(\"scrollBehavior\" in _body.style) && gsap.set(isViewport ? [_body, _docEl] : scroller, {scrollBehavior: \"auto\"}); // smooth scrolling doesn't work with snap.\n\t\t\t_scrollers.forEach(o => _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false)); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\t\t\tsnapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? (value, st) => _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction) : gsap.utils.snap(snap.snapTo);\n\t\t\tsnapDurClamp = snap.duration || {min: 0.1, max: 2};\n\t\t\tsnapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n\t\t\tsnapDelayedCall = gsap.delayedCall(snap.delay || (scrubSmooth / 2) || 0.1, () => {\n\t\t\t\tlet scroll = scrollFunc(),\n\t\t\t\t\trefreshedRecently = _getTime() - lastRefresh < 500,\n\t\t\t\t\ttween = tweenTo.tween;\n\t\t\t\tif ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n\t\t\t\t\tlet progress = (scroll - start) / change, // don't use self.progress because this might run between the refresh() and when the scroll position updates and self.progress is set properly in the update() method.\n\t\t\t\t\t\ttotalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n\t\t\t\t\t\tvelocity = refreshedRecently ? 0 : ((totalProgress - snap2) / (_getTime() - _time2) * 1000) || 0,\n\t\t\t\t\t\tchange1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n\t\t\t\t\t\tnaturalEnd = progress + (snap.inertia === false ? 0 : change1),\n\t\t\t\t\t\tendValue, endScroll,\n\t\t\t\t\t\t{ onStart, onInterrupt, onComplete } = snap;\n\t\t\t\t\tendValue = snapFunc(naturalEnd, self);\n\t\t\t\t\t_isNumber(endValue) || (endValue = naturalEnd); // in case the function didn't return a number, fall back to using the naturalEnd\n\t\t\t\t\tendScroll = Math.round(start + endValue * change);\n\t\t\t\t\tif (scroll <= end && scroll >= start && endScroll !== scroll) {\n\t\t\t\t\t\tif (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) { // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (snap.inertia === false) {\n\t\t\t\t\t\t\tchange1 = endValue - progress;\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttweenTo(endScroll, {\n\t\t\t\t\t\t\tduration: snapDurClamp(_abs( (Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05) || 0)),\n\t\t\t\t\t\t\tease: snap.ease || \"power3\",\n\t\t\t\t\t\t\tdata: _abs(endScroll - scroll), // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n\t\t\t\t\t\t\tonInterrupt: () => snapDelayedCall.restart(true) && onInterrupt && onInterrupt(self),\n\t\t\t\t\t\t\tonComplete() {\n\t\t\t\t\t\t\t\tself.update();\n\t\t\t\t\t\t\t\tlastSnap = scrollFunc();\n\t\t\t\t\t\t\t\tif (animation) { // the resolution of the scrollbar is limited, so we should correct the scrubbed animation's playhead at the end to match EXACTLY where it was supposed to snap\n\t\t\t\t\t\t\t\t\tscrubTween ? scrubTween.resetTo(\"totalProgress\", endValue, animation._tTime / animation._tDur) : animation.progress(endValue);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tsnap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n\t\t\t\t\t\t\t\tonSnapComplete && onSnapComplete(self);\n\t\t\t\t\t\t\t\tonComplete && onComplete(self);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, scroll, change1 * change, endScroll - scroll - change1 * change);\n\t\t\t\t\t\tonStart && onStart(self, tweenTo.tween);\n\t\t\t\t\t}\n\t\t\t\t} else if (self.isActive && lastSnap !== scroll) {\n\t\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t\t}\n\t\t\t}).pause();\n\t\t}\n\t\tid && (_ids[id] = self);\n\t\ttrigger = self.trigger = _getTarget(trigger || (pin !== true && pin));\n\n\t\t// if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\t\tcustomRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n\t\tcustomRevertReturn && (customRevertReturn = customRevertReturn(self));\n\n\t\tpin = pin === true ? trigger : _getTarget(pin);\n\t\t_isString(toggleClass) && (toggleClass = {targets: trigger, className: toggleClass});\n\t\tif (pin) {\n\t\t\t(pinSpacing === false || pinSpacing === _margin) || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\t\t\tself.pin = pin;\n\t\t\tpinCache = gsap.core.getCache(pin);\n\t\t\tif (!pinCache.spacer) { // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n\t\t\t\tif (pinSpacer) {\n\t\t\t\t\tpinSpacer = _getTarget(pinSpacer);\n\t\t\t\t\tpinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\t\t\t\t\tpinCache.spacerIsNative = !!pinSpacer;\n\t\t\t\t\tpinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n\t\t\t\t}\n\t\t\t\tpinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n\t\t\t\tspacer.classList.add(\"pin-spacer\");\n\t\t\t\tid && spacer.classList.add(\"pin-spacer-\" + id);\n\t\t\t\tpinCache.pinState = pinOriginalState = _getState(pin);\n\t\t\t} else {\n\t\t\t\tpinOriginalState = pinCache.pinState;\n\t\t\t}\n\t\t\tvars.force3D !== false && gsap.set(pin, {force3D: true});\n\t\t\tself.spacer = spacer = pinCache.spacer;\n\t\t\tcs = _getComputedStyle(pin);\n\t\t\tspacingStart = cs[pinSpacing + direction.os2];\n\t\t\tpinGetter = gsap.getProperty(pin);\n\t\t\tpinSetter = gsap.quickSetter(pin, direction.a, _px);\n\t\t\t// pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\tpinState = _getState(pin);\n\t\t}\n\t\tif (markers) {\n\t\t\tmarkerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n\t\t\tmarkerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n\t\t\tmarkerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n\t\t\toffset = markerStartTrigger[\"offset\" + direction.op.d2];\n\t\t\tlet content = _getTarget(_getProxyProp(scroller, \"content\") || scroller);\n\t\t\tmarkerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tmarkerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tcontainerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\t\t\tif ((!useFixedPosition && !(_proxies.length && _getProxyProp(scroller, \"fixedMarkers\") === true))) {\n\t\t\t\t_makePositionable(isViewport ? _body : scroller);\n\t\t\t\tgsap.set([markerStartTrigger, markerEndTrigger], {force3D: true});\n\t\t\t\tmarkerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n\t\t\t\tmarkerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n\t\t\t}\n\t\t}\n\n\t\tif (containerAnimation) {\n\t\t\tlet oldOnUpdate = containerAnimation.vars.onUpdate,\n\t\t\t\toldParams = containerAnimation.vars.onUpdateParams;\n\t\t\tcontainerAnimation.eventCallback(\"onUpdate\", () => {\n\t\t\t\tself.update(0, 0, 1);\n\t\t\t\toldOnUpdate && oldOnUpdate.apply(containerAnimation, oldParams || []);\n\t\t\t});\n\t\t}\n\n\t\tself.previous = () => _triggers[_triggers.indexOf(self) - 1];\n\t\tself.next = () => _triggers[_triggers.indexOf(self) + 1];\n\n\t\tself.revert = (revert, temp) => {\n\t\t\tif (!temp) { return self.kill(true); } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\t\t\tlet r = revert !== false || !self.enabled,\n\t\t\t\tprevRefreshing = _refreshing;\n\t\t\tif (r !== self.isReverted) {\n\t\t\t\tif (r) {\n\t\t\t\t\tprevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\t\t\t\t\tprevProgress = self.progress;\n\t\t\t\t\tprevAnimProgress = animation && animation.progress();\n\t\t\t\t}\n\t\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.style.display = r ? \"none\" : \"block\");\n\t\t\t\tif (r) {\n\t\t\t\t\t_refreshing = self;\n\t\t\t\t\tself.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n\t\t\t\t}\n\t\t\t\tif (pin && (!pinReparent || !self.isActive)) {\n\t\t\t\t\tif (r) {\n\t\t\t\t\t\t_swapPinOut(pin, spacer, pinOriginalState);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t_swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tr || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\t\t\t\t_refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\t\t\t\tself.isReverted = r;\n\t\t\t}\n\t\t}\n\n\t\tself.refresh = (soft, force, position, pinOffset) => { // position is typically only defined if it's coming from setPositions() - it's a way to skip the normal parsing. pinOffset is also only from setPositions() and is mostly related to fancy stuff we need to do in ScrollSmoother with effects\n\t\t\tif ((_refreshing || !self.enabled) && !force) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (pin && soft && _lastScrollTime) {\n\t\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t!_refreshingAll && onRefreshInit && onRefreshInit(self);\n\t\t\t_refreshing = self;\n\t\t\tif (tweenTo.tween && !position) { // we skip this if a position is passed in because typically that's from .setPositions() and it's best to allow in-progress snapping to continue.\n\t\t\t\ttweenTo.tween.kill();\n\t\t\t\ttweenTo.tween = 0;\n\t\t\t}\n\t\t\tscrubTween && scrubTween.pause();\n\t\t\tinvalidateOnRefresh && animation && animation.revert({kill: false}).invalidate();\n\t\t\tself.isReverted || self.revert(true, true);\n\t\t\tself._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\t\t\tlet size = getScrollerSize(),\n\t\t\t\tscrollerBounds = getScrollerOffsets(),\n\t\t\t\tmax = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n\t\t\t\tisFirstRefresh = change <= 0.01,\n\t\t\t\toffset = 0,\n\t\t\t\totherPinOffset = pinOffset || 0,\n\t\t\t\tparsedEnd = _isObject(position) ? position.end : vars.end,\n\t\t\t\tparsedEndTrigger = vars.endTrigger || trigger,\n\t\t\t\tparsedStart = _isObject(position) ? position.start : (vars.start || (vars.start === 0 || !trigger ? 0 : (pin ? \"0 0\" : \"0 100%\"))),\n\t\t\t\tpinnedContainer = self.pinnedContainer = vars.pinnedContainer && _getTarget(vars.pinnedContainer, self),\n\t\t\t\ttriggerIndex = (trigger && Math.max(0, _triggers.indexOf(self))) || 0,\n\t\t\t\ti = triggerIndex,\n\t\t\t\tcs, bounds, scroll, isVertical, override, curTrigger, curPin, oppositeScroll, initted, revertedPins, forcedOverflow, markerStartOffset, markerEndOffset;\n\t\t\tif (markers && _isObject(position)) { // if we alter the start/end positions with .setPositions(), it generally feeds in absolute NUMBERS which don't convey information about where to line up the markers, so to keep it intuitive, we record how far the trigger positions shift after applying the new numbers and then offset by that much in the opposite direction. We do the same to the associated trigger markers too of course.\n\t\t\t\tmarkerStartOffset = gsap.getProperty(markerStartTrigger, direction.p);\n\t\t\t\tmarkerEndOffset = gsap.getProperty(markerEndTrigger, direction.p);\n\t\t\t}\n\t\t\twhile (i--) { // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = self); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && (curPin === trigger || curPin === pin || curPin === pinnedContainer) && !curTrigger.isReverted) {\n\t\t\t\t\trevertedPins || (revertedPins = []);\n\t\t\t\t\trevertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\t\t\t\t\tcurTrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t\tif (curTrigger !== _triggers[i]) { // in case it got removed.\n\t\t\t\t\ttriggerIndex--;\n\t\t\t\t\ti--;\n\t\t\t\t}\n\t\t\t}\n\t\t\t_isFunction(parsedStart) && (parsedStart = parsedStart(self));\n\t\t\tparsedStart = _parseClamp(parsedStart, \"start\", self);\n\t\t\tstart = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._startClamp && \"_startClamp\") || (pin ? -0.001 : 0);\n\t\t\t_isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\t\t\tif (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n\t\t\t\tif (~parsedEnd.indexOf(\" \")) {\n\t\t\t\t\tparsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n\t\t\t\t} else {\n\t\t\t\t\toffset = _offsetToPx(parsedEnd.substr(2), size);\n\t\t\t\t\tparsedEnd = _isString(parsedStart) ? parsedStart : (containerAnimation ? gsap.utils.mapRange(0, containerAnimation.duration(), containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, start) : start) + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\t\t\t\t\tparsedEndTrigger = trigger;\n\t\t\t\t}\n\t\t\t}\n\t\t\tparsedEnd = _parseClamp(parsedEnd, \"end\", self);\n\t\t\tend = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._endClamp && \"_endClamp\")) || -0.001;\n\n\t\t\toffset = 0;\n\t\t\ti = triggerIndex;\n\t\t\twhile (i--) {\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n\t\t\t\t\tcs = curTrigger.end - (self._startClamp ? Math.max(0, curTrigger.start) : curTrigger.start);\n\t\t\t\t\tif (((curPin === trigger && curTrigger.start - curTrigger._pinPush < start) || curPin === pinnedContainer) && isNaN(parsedStart)) { // numeric start values shouldn't be offset at all - treat them as absolute\n\t\t\t\t\t\toffset += cs * (1 - curTrigger.progress);\n\t\t\t\t\t}\n\t\t\t\t\tcurPin === pin && (otherPinOffset += cs);\n\t\t\t\t}\n\t\t\t}\n\t\t\tstart += offset;\n\t\t\tend += offset;\n\t\t\tself._startClamp && (self._startClamp += offset);\n\n\t\t\tif (self._endClamp && !_refreshingAll) {\n\t\t\t\tself._endClamp = end || -0.001;\n\t\t\t\tend = Math.min(end, _maxScroll(scroller, direction));\n\t\t\t}\n\t\t\tchange = (end - start) || ((start -= 0.01) && 0.001);\n\n\t\t\tif (isFirstRefresh) { // on the very first refresh(), the prevProgress couldn't have been accurate yet because the start/end were never calculated, so we set it here. Before 3.11.5, it could lead to an inaccurate scroll position restoration with snapping.\n\t\t\t\tprevProgress = gsap.utils.clamp(0, 1, gsap.utils.normalize(start, end, prevScroll));\n\t\t\t}\n\t\t\tself._pinPush = otherPinOffset;\n\t\t\tif (markerStart && offset) { // offset the markers if necessary\n\t\t\t\tcs = {};\n\t\t\t\tcs[direction.a] = \"+=\" + offset;\n\t\t\t\tpinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n\t\t\t\tgsap.set([markerStart, markerEnd], cs);\n\t\t\t}\n\n\t\t\tif (pin && !(_clampingMax && self.end >= _maxScroll(scroller, direction))) {\n\t\t\t\tcs = _getComputedStyle(pin);\n\t\t\t\tisVertical = direction === _vertical;\n\t\t\t\tscroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\t\t\t\tpinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\t\t\t\tif (!max && end > 1) { // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://gsap.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n\t\t\t\t\tforcedOverflow = (isViewport ? (_doc.scrollingElement || _docEl) : scroller).style;\n\t\t\t\t\tforcedOverflow = {style: forcedOverflow, value: forcedOverflow[\"overflow\" + direction.a.toUpperCase()]};\n\t\t\t\t\tif (isViewport && _getComputedStyle(_body)[\"overflow\" + direction.a.toUpperCase()] !== \"scroll\") { // avoid an extra scrollbar if BOTH <html> and <body> have overflow set to \"scroll\"\n\t\t\t\t\t\tforcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = \"scroll\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\t\tpinState = _getState(pin);\n\t\t\t\t// transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\t\t\t\tbounds = _getBounds(pin, true);\n\t\t\t\toppositeScroll = useFixedPosition && _getScrollFunc(scroller, isVertical ? _horizontal : _vertical)();\n\t\t\t\tif (pinSpacing) {\n\t\t\t\t\tspacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n\t\t\t\t\tspacerState.t = spacer;\n\t\t\t\t\ti = (pinSpacing === _padding) ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\t\t\t\t\tif (i) {\n\t\t\t\t\t\tspacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\t\t\t\t\t\tspacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n\t\t\t\t\t}\n\t\t\t\t\t_setState(spacerState);\n\t\t\t\t\tif (pinnedContainer) { // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n\t\t\t\t\t\t_triggers.forEach(t => {\n\t\t\t\t\t\t\tif (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n\t\t\t\t\t\t\t\tt._subPinOffset = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tuseFixedPosition && scrollFunc(prevScroll);\n\t\t\t\t} else {\n\t\t\t\t\ti = _getSize(pin, direction);\n\t\t\t\t\ti && spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n\t\t\t\t}\n\t\t\t\tif (useFixedPosition) {\n\t\t\t\t\toverride = {\n\t\t\t\t\t\ttop: (bounds.top + (isVertical ? scroll - start : oppositeScroll)) + _px,\n\t\t\t\t\t\tleft: (bounds.left + (isVertical ? oppositeScroll : scroll - start)) + _px,\n\t\t\t\t\t\tboxSizing: \"border-box\",\n\t\t\t\t\t\tposition: \"fixed\"\n\t\t\t\t\t};\n\t\t\t\t\toverride[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n\t\t\t\t\toverride[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n\t\t\t\t\toverride[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n\t\t\t\t\toverride[_padding] = cs[_padding];\n\t\t\t\t\toverride[_padding + _Top] = cs[_padding + _Top];\n\t\t\t\t\toverride[_padding + _Right] = cs[_padding + _Right];\n\t\t\t\t\toverride[_padding + _Bottom] = cs[_padding + _Bottom];\n\t\t\t\t\toverride[_padding + _Left] = cs[_padding + _Left];\n\t\t\t\t\tpinActiveState = _copyState(pinOriginalState, override, pinReparent);\n\t\t\t\t\t_refreshingAll && scrollFunc(0);\n\t\t\t\t}\n\t\t\t\tif (animation) { // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n\t\t\t\t\tinitted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\t\t\t\t\t_suppressOverwrites(1);\n\t\t\t\t\tanimation.render(animation.duration(), true, true);\n\t\t\t\t\tpinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n\t\t\t\t\tpinMoves = Math.abs(change - pinChange) > 1;\n\t\t\t\t\tuseFixedPosition && pinMoves && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\t\t\t\t\tanimation.render(0, true, true);\n\t\t\t\t\tinitted || animation.invalidate(true);\n\t\t\t\t\tanimation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\t\t\t\t\t_suppressOverwrites(0);\n\t\t\t\t} else {\n\t\t\t\t\tpinChange = change\n\t\t\t\t}\n\t\t\t\tforcedOverflow && (forcedOverflow.value ? (forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = forcedOverflow.value) : forcedOverflow.style.removeProperty(\"overflow-\" + direction.a));\n\t\t\t} else if (trigger && scrollFunc() && !containerAnimation) { // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n\t\t\t\tbounds = trigger.parentNode;\n\t\t\t\twhile (bounds && bounds !== _body) {\n\t\t\t\t\tif (bounds._pinOffset) {\n\t\t\t\t\t\tstart -= bounds._pinOffset;\n\t\t\t\t\t\tend -= bounds._pinOffset;\n\t\t\t\t\t}\n\t\t\t\t\tbounds = bounds.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\trevertedPins && revertedPins.forEach(t => t.revert(false, true));\n\t\t\tself.start = start;\n\t\t\tself.end = end;\n\t\t\tscroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\t\t\tif (!containerAnimation && !_refreshingAll) {\n\t\t\t\tscroll1 < prevScroll && scrollFunc(prevScroll);\n\t\t\t\tself.scroll.rec = 0;\n\t\t\t}\n\t\t\tself.revert(false, true);\n\t\t\tlastRefresh = _getTime();\n\t\t\tif (snapDelayedCall) {\n\t\t\t\tlastSnap = -1; // just so snapping gets re-enabled, clear out any recorded last value\n\t\t\t\t// self.isActive && scrollFunc(start + change * prevProgress); // previously this line was here to ensure that when snapping kicks in, it's from the previous progress but in some cases that's not desirable, like an all-page ScrollTrigger when new content gets added to the page, that'd totally change the progress.\n\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t}\n\t\t\t_refreshing = 0;\n\t\t\tanimation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress || 0, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\t\t\tif (isFirstRefresh || prevProgress !== self.progress || containerAnimation || invalidateOnRefresh) { // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n\t\t\t\tanimation && !isToggle && animation.totalProgress(containerAnimation && start < -0.001 && !prevProgress ? gsap.utils.normalize(start, end, 0) : prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\t\t\t\tself.progress = isFirstRefresh || ((scroll1 - start) / change === prevProgress) ? 0 : prevProgress;\n\t\t\t}\n\t\t\tpin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n\t\t\tscrubTween && scrubTween.invalidate();\n\n\t\t\tif (!isNaN(markerStartOffset)) { // numbers were passed in for the position which are absolute, so instead of just putting the markers at the very bottom of the viewport, we figure out how far they shifted down (it's safe to assume they were originally positioned in closer relation to the trigger element with values like \"top\", \"center\", a percentage or whatever, so we offset that much in the opposite direction to basically revert them to the relative position thy were at previously.\n\t\t\t\tmarkerStartOffset -= gsap.getProperty(markerStartTrigger, direction.p);\n\t\t\t\tmarkerEndOffset -= gsap.getProperty(markerEndTrigger, direction.p);\n\t\t\t\t_shiftMarker(markerStartTrigger, direction, markerStartOffset);\n\t\t\t\t_shiftMarker(markerStart, direction, markerStartOffset - (pinOffset || 0));\n\t\t\t\t_shiftMarker(markerEndTrigger, direction, markerEndOffset);\n\t\t\t\t_shiftMarker(markerEnd, direction, markerEndOffset - (pinOffset || 0));\n\t\t\t}\n\n\t\t\tisFirstRefresh && !_refreshingAll && self.update(); // edge case - when you reload a page when it's already scrolled down, some browsers fire a \"scroll\" event before DOMContentLoaded, triggering an updateAll(). If we don't update the self.progress as part of refresh(), then when it happens next, it may record prevProgress as 0 when it really shouldn't, potentially causing a callback in an animation to fire again.\n\n\t\t\tif (onRefresh && !_refreshingAll && !executingOnRefresh) { // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n\t\t\t\texecutingOnRefresh = true;\n\t\t\t\tonRefresh(self);\n\t\t\t\texecutingOnRefresh = false;\n\t\t\t}\n\t\t};\n\n\t\tself.getVelocity = () => ((scrollFunc() - scroll2) / (_getTime() - _time2) * 1000) || 0;\n\n\t\tself.endAnimation = () => {\n\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\tif (animation) {\n\t\t\t\tscrubTween ? scrubTween.progress(1) : (!animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1));\n\t\t\t}\n\t\t};\n\n\t\tself.labelToScroll = label => animation && animation.labels && ((start || self.refresh() || start) + (animation.labels[label] / animation.duration()) * change) || 0;\n\n\t\tself.getTrailing = name => {\n\t\t\tlet i = _triggers.indexOf(self),\n\t\t\t\ta = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i+1);\n\t\t\treturn (_isString(name) ? a.filter(t => t.vars.preventOverlaps === name) : a).filter(t => self.direction > 0 ? t.end <= start : t.start >= end);\n\t\t};\n\n\n\t\tself.update = (reset, recordVelocity, forceFake) => {\n\t\t\tif (containerAnimation && !forceFake && !reset) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet scroll = _refreshingAll === true ? prevScroll : self.scroll(),\n\t\t\t\tp = reset ? 0 : (scroll - start) / change,\n\t\t\t\tclipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n\t\t\t\tprevProgress = self.progress,\n\t\t\t\tisActive, wasActive, toggleState, action, stateChanged, toggled, isAtMax, isTakingAction;\n\t\t\tif (recordVelocity) {\n\t\t\t\tscroll2 = scroll1;\n\t\t\t\tscroll1 = containerAnimation ? scrollFunc() : scroll;\n\t\t\t\tif (snap) {\n\t\t\t\t\tsnap2 = snap1;\n\t\t\t\t\tsnap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n\t\t\t\t}\n\t\t\t}\n\t\t\t// anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\t\t\tif (anticipatePin && pin && !_refreshing && !_startup && _lastScrollTime) {\n\t\t\t\tif (!clipped && start < scroll + ((scroll - scroll2) / (_getTime() - _time2)) * anticipatePin) {\n\t\t\t\t\tclipped = 0.0001;\n\t\t\t\t} else if (clipped === 1 && end > scroll + ((scroll - scroll2) / (_getTime() - _time2)) * anticipatePin) {\n\t\t\t\t\tclipped = 0.9999;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (clipped !== prevProgress && self.enabled) {\n\t\t\t\tisActive = self.isActive = !!clipped && clipped < 1;\n\t\t\t\twasActive = !!prevProgress && prevProgress < 1;\n\t\t\t\ttoggled = isActive !== wasActive;\n\t\t\t\tstateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\t\t\t\tself.direction = clipped > prevProgress ? 1 : -1;\n\t\t\t\tself.progress = clipped;\n\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\ttoggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\taction = (!toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1]) || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\t\t\t\t\t\tisTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tpreventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(t => t.endAnimation()));\n\n\t\t\t\tif (!isToggle) {\n\t\t\t\t\tif (scrubTween && !_refreshing && !_startup) {\n\t\t\t\t\t\t(scrubTween._dp._time - scrubTween._start !== scrubTween._time) && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\t\t\t\t\t\tif (scrubTween.resetTo) {\n\t\t\t\t\t\t\tscrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n\t\t\t\t\t\t} else { // legacy support (courtesy), before 3.10.0\n\t\t\t\t\t\t\tscrubTween.vars.totalProgress = clipped;\n\t\t\t\t\t\t\tscrubTween.invalidate().restart();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (animation) {\n\t\t\t\t\t\tanimation.totalProgress(clipped, !!(_refreshing && (lastRefresh || reset)));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pin) {\n\t\t\t\t\treset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\t\t\t\t\tif (!useFixedPosition) {\n\t\t\t\t\t\tpinSetter(_round(pinStart + pinChange * clipped));\n\t\t\t\t\t} else if (stateChanged) {\n\t\t\t\t\t\tisAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\t\t\t\t\t\tif (pinReparent) {\n\t\t\t\t\t\t\tif (!reset && (isActive || isAtMax)) {\n\t\t\t\t\t\t\t\tlet bounds = _getBounds(pin, true),\n\t\t\t\t\t\t\t\t\toffset = scroll - start;\n\t\t\t\t\t\t\t\t_reparent(pin, _body, (bounds.top + (direction === _vertical ? offset : 0)) + _px, (bounds.left + (direction === _vertical ? 0 : offset)) + _px);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t_reparent(pin, spacer);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_setState(isActive || isAtMax ? pinActiveState : pinState);\n\t\t\t\t\t\t(pinMoves && clipped < 1 && isActive) || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tsnap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n\t\t\t\ttoggleClass && (toggled || (once && clipped && (clipped < 1 || !_limitCallbacks))) && _toArray(toggleClass.targets).forEach(el => el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className)); // classes could affect positioning, so do it even if reset or refreshing is true.\n\t\t\t\tonUpdate && !isToggle && !reset && onUpdate(self);\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\tif (isTakingAction) {\n\t\t\t\t\t\t\tif (action === \"complete\") {\n\t\t\t\t\t\t\t\tanimation.pause().totalProgress(1);\n\t\t\t\t\t\t\t} else if (action === \"reset\") {\n\t\t\t\t\t\t\t\tanimation.restart(true).pause();\n\t\t\t\t\t\t\t} else if (action === \"restart\") {\n\t\t\t\t\t\t\t\tanimation.restart(true);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tanimation[action]();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonUpdate && onUpdate(self);\n\t\t\t\t\t}\n\t\t\t\t\tif (toggled || !_limitCallbacks) { // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n\t\t\t\t\t\tonToggle && toggled && _callback(self, onToggle);\n\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\tonce && (clipped === 1 ? self.kill(false, 1) : (callbacks[toggleState] = 0)); // a callback shouldn't be called again if once is true.\n\t\t\t\t\t\tif (!toggled) { // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n\t\t\t\t\t\t\ttoggleState = clipped === 1 ? 1 : 3;\n\t\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n\t\t\t\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\t\t\t\tscrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n\t\t\t\t\t}\n\t\t\t\t} else if (isToggle && onUpdate && !_refreshing) {\n\t\t\t\t\tonUpdate(self);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// update absolutely-positioned markers (only if the scroller isn't the viewport)\n\t\t\tif (markerEndSetter) {\n\t\t\t\tlet n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n\t\t\t\tmarkerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n\t\t\t\tmarkerEndSetter(n);\n\t\t\t}\n\t\t\tcaMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n\t\t};\n\n\t\tself.enable = (reset, refresh) => {\n\t\t\tif (!self.enabled) {\n\t\t\t\tself.enabled = true;\n\t\t\t\t_addListener(scroller, \"resize\", _onResize);\n\t\t\t\tisViewport || _addListener(scroller, \"scroll\", _onScroll);\n\t\t\t\tonRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (reset !== false) {\n\t\t\t\t\tself.progress = prevProgress = 0;\n\t\t\t\t\tscroll1 = scroll2 = lastSnap = scrollFunc();\n\t\t\t\t}\n\t\t\t\trefresh !== false && self.refresh();\n\t\t\t}\n\t\t};\n\n\t\tself.getTween = snap => snap && tweenTo ? tweenTo.tween : scrubTween;\n\n\t\tself.setPositions = (newStart, newEnd, keepClamp, pinOffset) => { // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n\t\t\tif (containerAnimation) { // convert ratios into scroll positions. Remember, start/end values on ScrollTriggers that have a containerAnimation refer to the time (in seconds), NOT scroll positions.\n\t\t\t\tlet st = containerAnimation.scrollTrigger,\n\t\t\t\t\tduration = containerAnimation.duration(),\n\t\t\t\t\tchange = st.end - st.start;\n\t\t\t\tnewStart = st.start + change * newStart / duration;\n\t\t\t\tnewEnd = st.start + change * newEnd / duration;\n\t\t\t}\n\t\t\tself.refresh(false, false, {start: _keepClamp(newStart, keepClamp && !!self._startClamp), end: _keepClamp(newEnd, keepClamp && !!self._endClamp)}, pinOffset);\n\t\t\tself.update();\n\t\t};\n\n\t\tself.adjustPinSpacing = amount => {\n\t\t\tif (spacerState && amount) {\n\t\t\t\tlet i = spacerState.indexOf(direction.d) + 1;\n\t\t\t\tspacerState[i] = (parseFloat(spacerState[i]) + amount) + _px;\n\t\t\t\tspacerState[1] = (parseFloat(spacerState[1]) + amount) + _px;\n\t\t\t\t_setState(spacerState);\n\t\t\t}\n\t\t};\n\n\t\tself.disable = (reset, allowAnimation) => {\n\t\t\tif (self.enabled) {\n\t\t\t\treset !== false && self.revert(true, true);\n\t\t\t\tself.enabled = self.isActive = false;\n\t\t\t\tallowAnimation || (scrubTween && scrubTween.pause());\n\t\t\t\tprevScroll = 0;\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\tonRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (snapDelayedCall) {\n\t\t\t\t\tsnapDelayedCall.pause();\n\t\t\t\t\ttweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n\t\t\t\t}\n\t\t\t\tif (!isViewport) {\n\t\t\t\t\tlet i = _triggers.length;\n\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\tif (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n\t\t\t\t\t\t\treturn; //don't remove the listeners if there are still other triggers referencing it.\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t_removeListener(scroller, \"resize\", _onResize);\n\t\t\t\t\tisViewport || _removeListener(scroller, \"scroll\", _onScroll);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tself.kill = (revert, allowAnimation) => {\n\t\t\tself.disable(revert, allowAnimation);\n\t\t\tscrubTween && !allowAnimation && scrubTween.kill();\n\t\t\tid && (delete _ids[id]);\n\t\t\tlet i = _triggers.indexOf(self);\n\t\t\ti >= 0 && _triggers.splice(i, 1);\n\t\t\ti === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n\n\t\t\t// if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\t\t\ti = 0;\n\t\t\t_triggers.forEach(t => t.scroller === self.scroller && (i = 1));\n\t\t\ti || _refreshingAll || (self.scroll.rec = 0);\n\n\t\t\tif (animation) {\n\t\t\t\tanimation.scrollTrigger = null;\n\t\t\t\trevert && animation.revert({kill: false});\n\t\t\t\tallowAnimation || animation.kill();\n\t\t\t}\n\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.parentNode && m.parentNode.removeChild(m));\n\t\t\t_primary === self && (_primary = 0);\n\t\t\tif (pin) {\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\ti = 0;\n\t\t\t\t_triggers.forEach(t => t.pin === pin && i++);\n\t\t\t\ti || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n\t\t\t}\n\t\t\tvars.onKill && vars.onKill(self);\n\t\t};\n\n\t\t_triggers.push(self);\n\t\tself.enable(false, false);\n\t\tcustomRevertReturn && customRevertReturn(self);\n\n\t\tif (animation && animation.add && !change) { // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n\t\t\tlet updateFunc = self.update; // some browsers may fire a scroll event BEFORE a tick elapses and/or the DOMContentLoaded fires. So there's a chance update() will be called BEFORE a refresh() has happened on a Timeline-attached ScrollTrigger which means the start/end won't be calculated yet. We don't want to add conditional logic inside the update() method (like check to see if end is defined and if not, force a refresh()) because that's a function that gets hit a LOT (performance). So we swap out the real update() method for this one that'll re-attach it the first time it gets called and of course forces a refresh().\n\t\t\tself.update = () => {\n\t\t\t\tself.update = updateFunc;\n\t\t\t\tstart || end || self.refresh();\n\t\t\t};\n\t\t\tgsap.delayedCall(0.01, self.update);\n\t\t\tchange = 0.01;\n\t\t\tstart = end = 0;\n\t\t} else {\n\t\t\tself.refresh();\n\t\t}\n\t\tpin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n\t}\n\n\n\tstatic register(core) {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = core || _getGSAP();\n\t\t\t_windowExists() && window.document && ScrollTrigger.enable();\n\t\t\t_coreInitted = _enabled;\n\t\t}\n\t\treturn _coreInitted;\n\t}\n\n\tstatic defaults(config) {\n\t\tif (config) {\n\t\t\tfor (let p in config) {\n\t\t\t\t_defaults[p] = config[p];\n\t\t\t}\n\t\t}\n\t\treturn _defaults;\n\t}\n\n\tstatic disable(reset, kill) {\n\t\t_enabled = 0;\n\t\t_triggers.forEach(trigger => trigger[kill ? \"kill\" : \"disable\"](reset));\n\t\t_removeListener(_win, \"wheel\", _onScroll);\n\t\t_removeListener(_doc, \"scroll\", _onScroll);\n\t\tclearInterval(_syncInterval);\n\t\t_removeListener(_doc, \"touchcancel\", _passThrough);\n\t\t_removeListener(_body, \"touchstart\", _passThrough);\n\t\t_multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t_multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t_resizeDelay.kill();\n\t\t_iterateAutoRefresh(_removeListener);\n\t\tfor (let i = 0; i < _scrollers.length; i+=3) {\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t}\n\t}\n\n\tstatic enable() {\n\t\t_win = window;\n\t\t_doc = document;\n\t\t_docEl = _doc.documentElement;\n\t\t_body = _doc.body;\n\t\tif (gsap) {\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || _passThrough;\n\t\t\t_suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n\t\t\t_scrollRestoration = _win.history.scrollRestoration || \"auto\";\n\t\t\t_lastScroll = _win.pageYOffset;\n\t\t\tgsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\t\t\tif (_body) {\n\t\t\t\t_enabled = 1;\n\t\t\t\t_div100vh = document.createElement(\"div\"); // to solve mobile browser address bar show/hide resizing, we shouldn't rely on window.innerHeight. Instead, use a <div> with its height set to 100vh and measure that since that's what the scrolling is based on anyway and it's not affected by address bar showing/hiding.\n\t\t\t\t_div100vh.style.height = \"100vh\";\n\t\t\t\t_div100vh.style.position = \"absolute\";\n\t\t\t\t_refresh100vh();\n\t\t\t\t_rafBugFix();\n\t\t\t\tObserver.register(gsap);\n\t\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t\tScrollTrigger.isTouch = Observer.isTouch;\n\t\t\t\t_fixIOSBug = Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\t\t\t\t_ignoreMobileResize = Observer.isTouch === 1;\n\t\t\t\t_addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\t\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t\tif (gsap.matchMedia) {\n\t\t\t\t\tScrollTrigger.matchMedia = vars => {\n\t\t\t\t\t\tlet mm = gsap.matchMedia(),\n\t\t\t\t\t\t\tp;\n\t\t\t\t\t\tfor (p in vars) {\n\t\t\t\t\t\t\tmm.add(p, vars[p]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn mm;\n\t\t\t\t\t};\n\t\t\t\t\tgsap.addEventListener(\"matchMediaInit\", () => _revertAll());\n\t\t\t\t\tgsap.addEventListener(\"matchMediaRevert\", () => _revertRecorded());\n\t\t\t\t\tgsap.addEventListener(\"matchMedia\", () => {\n\t\t\t\t\t\t_refreshAll(0, 1);\n\t\t\t\t\t\t_dispatch(\"matchMedia\");\n\t\t\t\t\t});\n\t\t\t\t\tgsap.matchMedia(\"(orientation: portrait)\", () => { // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n\t\t\t\t\t\t_setBaseDimensions();\n\t\t\t\t\t\treturn _setBaseDimensions;\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn(\"Requires GSAP 3.11.0 or later\");\n\t\t\t\t}\n\t\t\t\t_setBaseDimensions();\n\t\t\t\t_addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\t\t\t\tlet bodyStyle = _body.style,\n\t\t\t\t\tborder = bodyStyle.borderTopStyle,\n\t\t\t\t\tAnimationProto = gsap.core.Animation.prototype,\n\t\t\t\t\tbounds, i;\n\t\t\t\tAnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", { value: function() { return this.time(-0.01, true); }}); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\t\t\t\tbodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\t\t\t\tbounds = _getBounds(_body);\n\t\t\t\t_vertical.m = Math.round(bounds.top + _vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\t\t\t\t_horizontal.m = Math.round(bounds.left + _horizontal.sc()) || 0;\n\t\t\t\tborder ? (bodyStyle.borderTopStyle = border) : bodyStyle.removeProperty(\"border-top-style\");\n\t\t\t\t// TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\t\t\t\t_syncInterval = setInterval(_sync, 250);\n\t\t\t\tgsap.delayedCall(0.5, () => _startup = 0);\n\t\t\t\t_addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\t\t\t\t_addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t\t\t_transformProp = gsap.utils.checkPrefix(\"transform\");\n\t\t\t\t_stateProps.push(_transformProp);\n\t\t\t\t_coreInitted = _getTime();\n\t\t\t\t_resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n\t\t\t\t_autoRefresh = [_doc, \"visibilitychange\", () => {\n\t\t\t\t\tlet w = _win.innerWidth,\n\t\t\t\t\t\th = _win.innerHeight;\n\t\t\t\t\tif (_doc.hidden) {\n\t\t\t\t\t\t_prevWidth = w;\n\t\t\t\t\t\t_prevHeight = h;\n\t\t\t\t\t} else if (_prevWidth !== w || _prevHeight !== h) {\n\t\t\t\t\t\t_onResize();\n\t\t\t\t\t}\n\t\t\t\t}, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\t\t\t\t_iterateAutoRefresh(_addListener);\n\t\t\t\t_triggers.forEach(trigger => trigger.enable(0, 1));\n\t\t\t\tfor (i = 0; i < _scrollers.length; i+=3) {\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tstatic config(vars) {\n\t\t(\"limitCallbacks\" in vars) && (_limitCallbacks = !!vars.limitCallbacks);\n\t\tlet ms = vars.syncInterval;\n\t\tms && clearInterval(_syncInterval) || ((_syncInterval = ms) && setInterval(_sync, ms));\n\t\t(\"ignoreMobileResize\" in vars) && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\t\tif (\"autoRefreshEvents\" in vars) {\n\t\t\t_iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n\t\t\t_ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n\t\t}\n\t}\n\n\tstatic scrollerProxy(target, vars) {\n\t\tlet t = _getTarget(target),\n\t\t\ti = _scrollers.indexOf(t),\n\t\t\tisViewport = _isViewport(t);\n\t\tif (~i) {\n\t\t\t_scrollers.splice(i, isViewport ? 6 : 2);\n\t\t}\n\t\tif (vars) {\n\t\t\tisViewport ? _proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _proxies.unshift(t, vars);\n\t\t}\n\t}\n\n\tstatic clearMatchMedia(query) {\n\t\t_triggers.forEach(t => t._ctx && t._ctx.query === query && t._ctx.kill(true, true));\n\t}\n\n\tstatic isInViewport(element, ratio, horizontal) {\n\t\tlet bounds = (_isString(element) ? _getTarget(element) : element).getBoundingClientRect(),\n\t\t\toffset = bounds[horizontal ? _width : _height] * ratio || 0;\n\t\treturn horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n\t}\n\n\tstatic positionInViewport(element, referencePoint, horizontal) {\n\t\t_isString(element) && (element = _getTarget(element));\n\t\tlet bounds = element.getBoundingClientRect(),\n\t\t\tsize = bounds[horizontal ? _width : _height],\n\t\t\toffset = referencePoint == null ? size / 2 : ((referencePoint in _keywords) ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0);\n\t\treturn horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n\t}\n\n\tstatic killAll(allowListeners) {\n\t\t_triggers.slice(0).forEach(t => t.vars.id !== \"ScrollSmoother\" && t.kill());\n\t\tif (allowListeners !== true) {\n\t\t\tlet listeners = _listeners.killAll || [];\n\t\t\t_listeners = {};\n\t\t\tlisteners.forEach(f => f());\n\t\t}\n\t}\n\n}\n\nScrollTrigger.version = \"3.12.5\";\nScrollTrigger.saveStyles = targets => targets ? _toArray(targets).forEach(target => { // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n\tif (target && target.style) {\n\t\tlet i = _savedStyles.indexOf(target);\n\t\ti >= 0 && _savedStyles.splice(i, 5);\n\t\t_savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n\t}\n}) : _savedStyles;\nScrollTrigger.revert = (soft, media) => _revertAll(!soft, media);\nScrollTrigger.create = (vars, animation) => new ScrollTrigger(vars, animation);\nScrollTrigger.refresh = safe => safe ? _onResize() : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\nScrollTrigger.update = force => ++_scrollers.cache && _updateAll(force === true ? 2 : 0);\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\nScrollTrigger.maxScroll = (element, horizontal) => _maxScroll(element, horizontal ? _horizontal : _vertical);\nScrollTrigger.getScrollFunc = (element, horizontal) => _getScrollFunc(_getTarget(element), horizontal ? _horizontal : _vertical);\nScrollTrigger.getById = id => _ids[id];\nScrollTrigger.getAll = () => _triggers.filter(t => t.vars.id !== \"ScrollSmoother\"); // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\nScrollTrigger.isScrolling = () => !!_lastScrollTime;\nScrollTrigger.snapDirectional = _snapDirectional;\nScrollTrigger.addEventListener = (type, callback) => {\n\tlet a = _listeners[type] || (_listeners[type] = []);\n\t~a.indexOf(callback) || a.push(callback);\n};\nScrollTrigger.removeEventListener = (type, callback) => {\n\tlet a = _listeners[type],\n\t\ti = a && a.indexOf(callback);\n\ti >= 0 && a.splice(i, 1);\n};\nScrollTrigger.batch = (targets, vars) => {\n\tlet result = [],\n\t\tvarsCopy = {},\n\t\tinterval = vars.interval || 0.016,\n\t\tbatchMax = vars.batchMax || 1e9,\n\t\tproxyCallback = (type, callback) => {\n\t\t\tlet elements = [],\n\t\t\t\ttriggers = [],\n\t\t\t\tdelay = gsap.delayedCall(interval, () => {callback(elements, triggers); elements = []; triggers = [];}).pause();\n\t\t\treturn self => {\n\t\t\t\telements.length || delay.restart(true);\n\t\t\t\telements.push(self.trigger);\n\t\t\t\ttriggers.push(self);\n\t\t\t\tbatchMax <= elements.length && delay.progress(1);\n\t\t\t};\n\t\t},\n\t\tp;\n\tfor (p in vars) {\n\t\tvarsCopy[p] = (p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\") ? proxyCallback(p, vars[p]) : vars[p];\n\t}\n\tif (_isFunction(batchMax)) {\n\t\tbatchMax = batchMax();\n\t\t_addListener(ScrollTrigger, \"refresh\", () => batchMax = vars.batchMax());\n\t}\n\t_toArray(targets).forEach(target => {\n\t\tlet config = {};\n\t\tfor (p in varsCopy) {\n\t\t\tconfig[p] = varsCopy[p];\n\t\t}\n\t\tconfig.trigger = target;\n\t\tresult.push(ScrollTrigger.create(config));\n\t});\n\treturn result;\n}\n\n\n// to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\nlet _clampScrollAndGetDurationMultiplier = (scrollFunc, current, end, max) => {\n\t\tcurrent > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n\t\treturn end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n\t},\n\t_allowNativePanning = (target, direction) => {\n\t\tif (direction === true) {\n\t\t\ttarget.style.removeProperty(\"touch-action\");\n\t\t} else {\n\t\t\ttarget.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n\t\t}\n\t\ttarget === _docEl && _allowNativePanning(_body, direction);\n\t},\n\t_overflow = {auto: 1, scroll: 1},\n\t_nestedScroll = ({event, target, axis}) => {\n\t\tlet node = (event.changedTouches ? event.changedTouches[0] : event).target,\n\t\t\tcache = node._gsap || gsap.core.getCache(node),\n\t\t\ttime = _getTime(), cs;\n\t\tif (!cache._isScrollT || time - cache._isScrollT > 2000) { // cache for 2 seconds to improve performance.\n\t\t\twhile (node && node !== _body && ((node.scrollHeight <= node.clientHeight && node.scrollWidth <= node.clientWidth) || !(_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]))) node = node.parentNode;\n\t\t\tcache._isScroll = node && node !== target && !_isViewport(node) && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n\t\t\tcache._isScrollT = time;\n\t\t}\n\t\tif (cache._isScroll || axis === \"x\") {\n\t\t\tevent.stopPropagation();\n\t\t\tevent._gsapAllow = true;\n\t\t}\n\t},\n\t// capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n\t_inputObserver = (target, type, inputs, nested) => Observer.create({\n\t\ttarget: target,\n\t\tcapture: true,\n\t\tdebounce: false,\n\t\tlockAxis: true,\n\t\ttype: type,\n\t\tonWheel: (nested = nested && _nestedScroll),\n\t\tonPress: nested,\n\t\tonDrag: nested,\n\t\tonScroll: nested,\n\t\tonEnable: () => inputs && _addListener(_doc, Observer.eventTypes[0], _captureInputs, false, true),\n\t\tonDisable: () => _removeListener(_doc, Observer.eventTypes[0], _captureInputs, true)\n\t}),\n\t_inputExp = /(input|label|select|textarea)/i,\n\t_inputIsFocused,\n\t_captureInputs = e => {\n\t\tlet isInput = _inputExp.test(e.target.tagName);\n\t\tif (isInput || _inputIsFocused) {\n\t\t\te._gsapAllow = true;\n\t\t\t_inputIsFocused = isInput;\n\t\t}\n\t},\n\t_getScrollNormalizer = vars => {\n\t\t_isObject(vars) || (vars = {});\n\t\tvars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n\t\tvars.type || (vars.type = \"wheel,touch\");\n\t\tvars.debounce = !!vars.debounce;\n\t\tvars.id = vars.id || \"normalizer\";\n\t\tlet {normalizeScrollX, momentum, allowNestedScroll, onRelease} = vars,\n\t\t\tself, maxY,\n\t\t\ttarget = _getTarget(vars.target) || _docEl,\n\t\t\tsmoother = gsap.core.globals().ScrollSmoother,\n\t\t\tsmootherInstance = smoother && smoother.get(),\n\t\t\tcontent = _fixIOSBug && ((vars.content && _getTarget(vars.content)) || (smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content())),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscale = 1,\n\t\t\tinitialScale = (Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n\t\t\twheelRefresh = 0,\n\t\t\tresolveMomentumDuration = _isFunction(momentum) ? () => momentum(self) : () => momentum || 2.8,\n\t\t\tlastRefreshID, skipTouchMove,\n\t\t\tinputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n\t\t\tresumeTouchMove = () => skipTouchMove = false,\n\t\t\tscrollClampX = _passThrough,\n\t\t\tscrollClampY = _passThrough,\n\t\t\tupdateClamps = () => {\n\t\t\t\tmaxY = _maxScroll(target, _vertical);\n\t\t\t\tscrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n\t\t\t\tnormalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _horizontal)));\n\t\t\t\tlastRefreshID = _refreshID;\n\t\t\t},\n\t\t\tremoveContentOffset = () => {\n\t\t\t\tcontent._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n\t\t\t\tscrollFuncY.offset = scrollFuncY.cacheID = 0;\n\t\t\t},\n\t\t\tignoreDrag = () => {\n\t\t\t\tif (skipTouchMove) {\n\t\t\t\t\trequestAnimationFrame(resumeTouchMove);\n\t\t\t\t\tlet offset = _round(self.deltaY / 2),\n\t\t\t\t\t\tscroll = scrollClampY(scrollFuncY.v - offset);\n\t\t\t\t\tif (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n\t\t\t\t\t\tscrollFuncY.offset = scroll - scrollFuncY.v;\n\t\t\t\t\t\tlet y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\t\t\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n\t\t\t\t\t\tcontent._gsap.y = y + \"px\";\n\t\t\t\t\t\tscrollFuncY.cacheID = _scrollers.cache;\n\t\t\t\t\t\t_updateAll();\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tskipTouchMove = true;\n\t\t\t},\n\t\t\ttween, startScrollX, startScrollY, onStopDelayedCall,\n\t\t\tonResize = () => { // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n\t\t\t\tupdateClamps();\n\t\t\t\tif (tween.isActive() && tween.vars.scrollY > maxY) {\n\t\t\t\t\tscrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n\t\t\t\t}\n\t\t\t};\n\t\tcontent && gsap.set(content, {y: \"+=0\"}); // to ensure there's a cache (element._gsap)\n\t\tvars.ignoreCheck = e => (_fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e)) || (scale > 1.05 && e.type !== \"touchstart\") || self.isGesturing || (e.touches && e.touches.length > 1);\n\t\tvars.onPress = () => {\n\t\t\tskipTouchMove = false;\n\t\t\tlet prevScale = scale;\n\t\t\tscale = _round(((_win.visualViewport && _win.visualViewport.scale) || 1) / initialScale);\n\t\t\ttween.pause();\n\t\t\tprevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n\t\t\tstartScrollX = scrollFuncX();\n\t\t\tstartScrollY = scrollFuncY();\n\t\t\tupdateClamps();\n\t\t\tlastRefreshID = _refreshID;\n\t\t}\n\t\tvars.onRelease = vars.onGestureStart = (self, wasDragging) => {\n\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\tif (!wasDragging) {\n\t\t\t\tonStopDelayedCall.restart(true);\n\t\t\t} else {\n\t\t\t\t_scrollers.cache++; // make sure we're pulling the non-cached value\n\t\t\t\t// alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\t\t\t\tlet dur = resolveMomentumDuration(),\n\t\t\t\t\tcurrentScroll, endScroll;\n\t\t\t\tif (normalizeScrollX) {\n\t\t\t\t\tcurrentScroll = scrollFuncX();\n\t\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityX) / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\t\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _horizontal));\n\t\t\t\t\ttween.vars.scrollX = scrollClampX(endScroll);\n\t\t\t\t}\n\t\t\t\tcurrentScroll = scrollFuncY();\n\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityY) / 0.227; // the constant .227 is from power4(0.05)\n\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _vertical));\n\t\t\t\ttween.vars.scrollY = scrollClampY(endScroll);\n\t\t\t\ttween.invalidate().duration(dur).play(0.01);\n\t\t\t\tif (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY-1) { // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n\t\t\t\t\tgsap.to({}, {onUpdate: onResize, duration: dur});\n\t\t\t\t}\n\t\t\t}\n\t\t\tonRelease && onRelease(self);\n\t\t};\n\t\tvars.onWheel = () => {\n\t\t\ttween._ts && tween.pause();\n\t\t\tif (_getTime() - wheelRefresh > 1000) { // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n\t\t\t\tlastRefreshID = 0;\n\t\t\t\twheelRefresh = _getTime();\n\t\t\t}\n\t\t};\n\t\tvars.onChange = (self, dx, dy, xArray, yArray) => {\n\t\t\t_refreshID !== lastRefreshID && updateClamps();\n\t\t\tdx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\t\t\tif (dy) {\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tlet isTouch = yArray[2] === dy,\n\t\t\t\t\ty = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n\t\t\t\t\tyClamped = scrollClampY(y);\n\t\t\t\tisTouch && y !== yClamped && (startScrollY += yClamped - y);\n\t\t\t\tscrollFuncY(yClamped);\n\t\t\t}\n\t\t\t(dy || dx) && _updateAll();\n\t\t};\n\t\tvars.onEnable = () => {\n\t\t\t_allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\t\t\tScrollTrigger.addEventListener(\"refresh\", onResize);\n\t\t\t_addListener(_win, \"resize\", onResize);\n\t\t\tif (scrollFuncY.smooth) {\n\t\t\t\tscrollFuncY.target.style.scrollBehavior = \"auto\";\n\t\t\t\tscrollFuncY.smooth = scrollFuncX.smooth = false;\n\t\t\t}\n\t\t\tinputObserver.enable();\n\t\t};\n\t\tvars.onDisable = () => {\n\t\t\t_allowNativePanning(target, true);\n\t\t\t_removeListener(_win, \"resize\", onResize);\n\t\t\tScrollTrigger.removeEventListener(\"refresh\", onResize);\n\t\t\tinputObserver.kill();\n\t\t};\n\t\tvars.lockAxis = vars.lockAxis !== false;\n\t\tself = new Observer(vars);\n\t\tself.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\t\t_fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\t\t_fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\t\tonStopDelayedCall = self._dc;\n\t\ttween = gsap.to(self, {ease: \"power4\", paused: true, inherit: false, scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\", scrollY: \"+=0.1\", modifiers: {scrollY: _interruptionTracker(scrollFuncY, scrollFuncY(), () => tween.pause())\t}, onUpdate: _updateAll, onComplete: onStopDelayedCall.vars.onComplete}); // we need the modifier to sense if the scroll position is altered outside of the momentum tween (like with a scrollTo tween) so we can pause() it to prevent conflicts.\n\t\treturn self;\n\t};\n\nScrollTrigger.sort = func => _triggers.sort(func || ((a, b) => (a.vars.refreshPriority || 0) * -1e6 + a.start - (b.start + (b.vars.refreshPriority || 0) * -1e6)));\nScrollTrigger.observe = vars => new Observer(vars);\nScrollTrigger.normalizeScroll = vars => {\n\tif (typeof(vars) === \"undefined\") {\n\t\treturn _normalizer;\n\t}\n\tif (vars === true && _normalizer) {\n\t\treturn _normalizer.enable();\n\t}\n\tif (vars === false) {\n\t\t_normalizer && _normalizer.kill();\n\t\t_normalizer = vars;\n\t\treturn;\n\t}\n\tlet normalizer = vars instanceof Observer ? vars : _getScrollNormalizer(vars);\n\t_normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n\t_isViewport(normalizer.target) && (_normalizer = normalizer);\n\treturn normalizer;\n};\n\n\nScrollTrigger.core = { // smaller file size way to leverage in ScrollSmoother and Observer\n\t_getVelocityProp,\n\t_inputObserver,\n\t_scrollers,\n\t_proxies,\n\tbridge: {\n\t\t// when normalizeScroll sets the scroll position (ss = setScroll)\n\t\tss: () => {\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t},\n\t\t// a way to get the _refreshing value in Observer\n\t\tref: () => _refreshing\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\n\nexport { ScrollTrigger as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_getProxyProp", "element", "property", "_proxies", "indexOf", "_isViewport", "el", "_root", "_addListener", "type", "func", "passive", "capture", "addEventListener", "_removeListener", "removeEventListener", "_onScroll", "_normalizer", "isPressed", "_scrollers", "cache", "_scrollCacheFunc", "f", "doNotCache", "cachingFunc", "value", "_startup", "_win", "history", "scrollRestoration", "isNormalizing", "v", "Math", "round", "iOS", "cacheID", "_bridge", "offset", "_getTarget", "t", "self", "_ctx", "selector", "utils", "toArray", "config", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "console", "warn", "_getScrollFunc", "s", "sc", "_doc", "scrollingElement", "_docEl", "i", "_vertical", "push", "prev", "arguments", "length", "target", "smooth", "getProperty", "_getVelocityProp", "minTimeRefresh", "useDel<PERSON>", "update", "force", "_getTime", "min", "t1", "v2", "v1", "t2", "dropToZeroTime", "max", "reset", "getVelocity", "latestValue", "tOld", "vOld", "_getEvent", "e", "preventDefault", "_gsapAllow", "changedTouches", "_getAbsoluteMax", "a", "abs", "_setScrollTrigger", "ScrollTrigger", "core", "globals", "_integrate", "data", "bridge", "scrollers", "proxies", "name", "_initCore", "_coreInitted", "document", "body", "documentElement", "_body", "clamp", "_context", "context", "_pointerType", "_isTouch", "Observer", "is<PERSON><PERSON>ch", "matchMedia", "matches", "navigator", "maxTouchPoints", "msMaxTouchPoints", "_eventTypes", "eventTypes", "split", "setTimeout", "_observers", "Date", "now", "_scrollLeft", "_scrollTop", "_horizontal", "p", "p2", "os", "os2", "d", "d2", "scrollTo", "pageXOffset", "op", "pageYOffset", "init", "vars", "tolerance", "dragMinimum", "lineHeight", "debounce", "onStop", "onStopDelay", "ignore", "wheelSpeed", "event", "onDragStart", "onDragEnd", "onDrag", "onPress", "onRelease", "onRight", "onLeft", "onUp", "onDown", "onChangeX", "onChangeY", "onChange", "onToggleX", "onToggleY", "onHover", "onHoverEnd", "onMove", "<PERSON><PERSON><PERSON><PERSON>", "isNormalizer", "onGestureStart", "onGestureEnd", "onWheel", "onEnable", "onDisable", "onClick", "scrollSpeed", "allowClicks", "lockAxis", "onLockAxis", "clickCapture", "onClickTime", "_ignore<PERSON>heck", "isPointerOr<PERSON>ouch", "limitToTouch", "pointerType", "dx", "deltaX", "dy", "deltaY", "changedX", "changedY", "prevDeltaX", "prevDeltaY", "moved", "dragged", "locked", "wheeled", "id", "onDelta", "x", "y", "index", "_vx", "_vy", "requestAnimationFrame", "onTouchOrPointerDelta", "axis", "_onDrag", "clientX", "clientY", "isDragging", "startX", "startY", "_onGestureStart", "touches", "isGesturing", "_onGestureEnd", "onScroll", "scrollFuncX", "scrollFuncY", "scrollX", "scrollY", "onStopDelayedCall", "restart", "_onWheel", "multiplier", "deltaMode", "innerHeight", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "parseFloat", "getComputedStyle", "this", "isViewport", "ownerDoc", "ownerDocument", "_onPress", "button", "pause", "_onRelease", "isTrackingDrag", "isNaN", "wasDragging", "isDragNotClick", "eventData", "delayedCall", "defaultPrevented", "click", "createEvent", "syntheticEvent", "initMouseEvent", "screenX", "screenY", "dispatchEvent", "_dc", "onStopFunc", "enable", "isEnabled", "disable", "filter", "o", "kill", "revert", "splice", "version", "create", "register", "getAll", "slice", "getById", "_parseClamp", "_isString", "substr", "_keepClamp", "_pointerDownHandler", "_pointerIsDown", "_pointerU<PERSON><PERSON><PERSON><PERSON>", "_passThrough", "_round", "_windowExists", "_getViewportDimension", "dimensionProperty", "_100vh", "_getBoundsFunc", "_winOffsets", "width", "innerWidth", "height", "_getBounds", "_maxScroll", "_iterateAutoRefresh", "events", "_autoRefresh", "_isFunction", "_isNumber", "_isObject", "_endAnimation", "animation", "reversed", "progress", "_callback", "enabled", "result", "add", "totalTime", "callbackAnimation", "_getComputedStyle", "_setDefaults", "obj", "defaults", "_getSize", "_getLabelRatioArray", "timeline", "labels", "duration", "_snapDirectional", "snapIncrementOrArray", "snap", "Array", "isArray", "sort", "b", "direction", "threshold", "snapped", "_multiListener", "types", "callback", "for<PERSON>ach", "nonPassive", "_wheelListener", "scrollFunc", "wheelHandler", "_offsetToPx", "size", "eqIndex", "relative", "char<PERSON>t", "_keywords", "_createMarker", "container", "matchWidthEl", "containerAnimation", "startColor", "endColor", "fontSize", "indent", "fontWeight", "createElement", "useFixedPosition", "isScroller", "parent", "isStart", "color", "css", "_right", "_bottom", "offsetWidth", "_isStart", "setAttribute", "style", "cssText", "innerText", "children", "insertBefore", "append<PERSON><PERSON><PERSON>", "_offset", "_position<PERSON><PERSON>er", "_sync", "_lastScrollTime", "_rafID", "_updateAll", "clientWidth", "_dispatch", "_setBaseDimensions", "_baseScreenWidth", "_baseScreenHeight", "_onResize", "_refreshing", "_ignoreResize", "fullscreenElement", "webkitFullscreenElement", "_ignoreMobileResize", "_resizeDelay", "_softRefresh", "_refreshAll", "_revertRecorded", "media", "_savedStyles", "query", "getBBox", "uncache", "_revertAll", "trigger", "_i", "_triggers", "_isReverted", "_clearScrollMemory", "_refreshingAll", "rec", "_scrollRestoration", "_refresh100vh", "_div100vh", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "_hideAllMarkers", "hide", "_toArray", "display", "_swapPinIn", "pin", "spacer", "cs", "spacerState", "_gsap", "swappedIn", "_propNamesToCopy", "spacerStyle", "pinStyle", "position", "flexBasis", "overflow", "boxSizing", "_width", "_px", "_height", "_padding", "_margin", "_setState", "parentNode", "_getState", "l", "_stateProps", "state", "_parsePosition", "scrollerSize", "scroll", "marker", "markerScroller", "scrollerBounds", "borderWidth", "scrollerMax", "clampZeroProp", "p1", "time", "seek", "mapRange", "scrollTrigger", "start", "end", "bounds", "localOffset", "globalOffset", "offsets", "left", "top", "removeProperty", "m", "_caScrollDist", "_reparent", "_st<PERSON><PERSON>", "_prefixExp", "test", "getCache", "_interruptionTracker", "getValueFunc", "initialValue", "onInterrupt", "last1", "last2", "current", "_shiftMarker", "set", "_getTweenCreator", "scroller", "getTween", "change1", "change2", "tween", "onComplete", "modifiers", "getScroll", "checkForInterruption", "prop", "inherit", "ratio", "onUpdate", "call", "to", "_clamp", "_time2", "_syncInterval", "_transformProp", "_prevWidth", "_prevHeight", "_sort", "_suppressOverwrites", "_fixIOSBug", "_clampingMax", "_limitCallbacks", "_queueRefreshID", "_primary", "_time1", "_enabled", "_abs", "_Right", "_Left", "_Top", "_Bottom", "_Width", "_Height", "withoutTransforms", "xPercent", "yPercent", "rotation", "rotationX", "rotationY", "scale", "skewX", "skewY", "getBoundingClientRect", "_markerDefaults", "_defaults", "toggleActions", "anticipatePin", "center", "bottom", "right", "flipped", "side", "oppositeSide", "_isFlipped", "_ids", "_listeners", "_emptyArray", "map", "_refreshID", "<PERSON><PERSON><PERSON><PERSON>", "isRefreshing", "refreshInits", "scroll<PERSON>eh<PERSON>or", "refresh", "_subPinOffset", "horizontal", "original", "adjustPinSpacing", "_dir", "endClamp", "_endClamp", "startClamp", "_startClamp", "setPositions", "render", "onRefresh", "_lastScroll", "_direction", "isUpdating", "recordVelocity", "concat", "_capsExp", "replace", "toLowerCase", "tweenTo", "pinCache", "snapFunc", "scroll1", "scroll2", "markerStart", "markerEnd", "markerStartTrigger", "markerEndTrigger", "markerVars", "executingOnRefresh", "change", "pinOriginalState", "pinActiveState", "pinState", "pinGetter", "pinSetter", "pinStart", "pinChange", "spacingStart", "markerStartSetter", "pinMoves", "markerEndSetter", "snap1", "snap2", "scrubTween", "scrubSmooth", "snapDurClamp", "snapDelayedCall", "prevScroll", "prevAnimProgress", "caMarkerSetter", "customRevertReturn", "nodeType", "toggleClass", "onToggle", "scrub", "pinSpacing", "invalidateOnRefresh", "onScrubComplete", "onSnapComplete", "once", "pinReparent", "pinSpacer", "fastScrollEnd", "preventOverlaps", "isToggle", "scrollerCache", "pinType", "callbacks", "onEnter", "onLeave", "onEnterBack", "onLeaveBack", "markers", "onRefreshInit", "getScrollerSize", "_getSizeFunc", "getScrollerOffsets", "_getOffsetsFunc", "lastSnap", "lastRefresh", "prevProgress", "bind", "refreshPriority", "tweenScroll", "scrubDuration", "ease", "totalProgress", "paused", "lazy", "_initted", "isReverted", "immediateRender", "snapTo", "_getClosestLabel", "_getLabelAtDirection", "st", "directional", "delay", "refreshedRecently", "isActive", "endValue", "endScroll", "velocity", "naturalEnd", "inertia", "onStart", "resetTo", "_tTime", "_tDur", "stRevert", "targets", "className", "nativeElement", "spacerIsNative", "classList", "force3D", "quickSetter", "content", "_makePositionable", "oldOnUpdate", "oldParams", "onUpdateParams", "eventCallback", "apply", "previous", "next", "temp", "r", "prevRefreshing", "_swapPinOut", "soft", "pinOffset", "invalidate", "isVertical", "override", "curTrigger", "curPin", "oppositeScroll", "initted", "revertedPins", "forcedOverflow", "markerStartOffset", "markerEndOffset", "isFirstRefresh", "otherPinOffset", "parsedEnd", "parsedEndTrigger", "endTrigger", "parsedStart", "<PERSON><PERSON><PERSON><PERSON>", "triggerIndex", "unshift", "_pinPush", "normalize", "_pinOffset", "toUpperCase", "ceil", "_copyState", "omitOffsets", "endAnimation", "labelToScroll", "label", "getTrailing", "reverse", "forceFake", "toggleState", "action", "stateChanged", "toggled", "isAtMax", "isTakingAction", "clipped", "_dp", "_time", "_start", "n", "newStart", "newEnd", "keepClamp", "amount", "allowAnimation", "onKill", "updateFunc", "_queueRefreshAll", "clearInterval", "suppressOverwrites", "_rafBugFix", "userAgent", "mm", "bodyStyle", "border", "borderTopStyle", "AnimationProto", "Animation", "prototype", "Object", "defineProperty", "setInterval", "checkPrefix", "w", "h", "hidden", "limitCallbacks", "ms", "syncInterval", "ignoreMobileResize", "autoRefreshEvents", "scrollerProxy", "clearMatchMedia", "isInViewport", "positionInViewport", "referencePoint", "killAll", "allowListeners", "listeners", "saveStyles", "getAttribute", "safe", "clearScrollMemory", "maxScroll", "getScrollFunc", "isScrolling", "snapDirectional", "batch", "proxyCallback", "elements", "triggers", "interval", "batchMax", "varsCopy", "_clampScrollAndGetDurationMultiplier", "_allowNativePanning", "touchAction", "_nestedScroll", "node", "_isScrollT", "scrollHeight", "clientHeight", "scrollWidth", "_overflow", "overflowY", "overflowX", "_isScroll", "stopPropagation", "_inputObserver", "inputs", "nested", "_captureInputs", "_getScrollNormalizer", "resumeTouchMove", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateClamps", "maxY", "scrollClampY", "normalizeScrollX", "scrollClampX", "lastRefreshID", "removeContentOffset", "transform", "onResize", "startScrollX", "startScrollY", "momentum", "allowNestedScroll", "smoother", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smootherInstance", "get", "initialScale", "visualViewport", "outerWidth", "wheelRefresh", "resolveMomentumDuration", "inputObserver", "ignoreDrag", "prevScale", "currentScroll", "dur", "velocityX", "velocityY", "play", "_ts", "xArray", "yArray", "yClamped", "ticker", "_inputIsFocused", "auto", "_inputExp", "isInput", "tagName", "observe", "normalizeScroll", "normalizer", "ss", "ref"], "mappings": ";;;;;;;;;mYAYY,SAAXA,WAAiBC,IAA4B,oBAAZC,SAA4BD,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAkB3F,SAAhBG,EAAiBC,EAASC,UAAcC,GAASC,QAAQH,IAAYE,GAASA,GAASC,QAAQH,GAAW,GAAGC,GAC/F,SAAdG,EAAcC,YAASC,EAAMH,QAAQE,GACtB,SAAfE,EAAgBP,EAASQ,EAAMC,EAAMC,EAASC,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACC,SAAqB,IAAZA,EAAmBC,UAAWA,IACrH,SAAlBE,EAAmBb,EAASQ,EAAMC,EAAME,UAAYX,EAAQc,oBAAoBN,EAAMC,IAAQE,GAGlF,SAAZI,WAAmBC,IAAeA,GAAYC,WAAcC,GAAWC,QACpD,SAAnBC,EAAoBC,EAAGC,GACJ,SAAdC,GAAcC,MACbA,GAAmB,IAAVA,EAAa,CACzBC,IAAaC,GAAKC,QAAQC,kBAAoB,cAC1CC,EAAgBb,IAAeA,GAAYC,UAC/CO,EAAQD,GAAYO,EAAIC,KAAKC,MAAMR,KAAWR,IAAeA,GAAYiB,IAAM,EAAI,GACnFZ,EAAEG,GACFD,GAAYW,QAAUhB,GAAWC,MACjCU,GAAiBM,EAAQ,KAAMX,QACrBF,GAAcJ,GAAWC,QAAUI,GAAYW,SAAWC,EAAQ,UAC5EZ,GAAYW,QAAUhB,GAAWC,MACjCI,GAAYO,EAAIT,YAEVE,GAAYO,EAAIP,GAAYa,cAEpCb,GAAYa,OAAS,EACdf,GAAKE,GAIA,SAAbc,EAAcC,EAAGC,UAAWA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,UAAa7C,GAAK8C,MAAMC,SAASL,GAAG,KAAqB,iBAAPA,IAAoD,IAAjC1C,GAAKgD,SAASC,eAA2BC,QAAQC,KAAK,qBAAsBT,GAAK,MAEhM,SAAjBU,EAAkBhD,SAAUiD,IAAAA,EAAGC,IAAAA,GAC9B9C,EAAYJ,KAAaA,EAAUmD,GAAKC,kBAAoBC,QACxDC,EAAIpC,GAAWf,QAAQH,GAC1BoC,EAASc,IAAOK,GAAUL,GAAK,EAAI,GAClCI,IAAMA,EAAIpC,GAAWsC,KAAKxD,GAAW,GACvCkB,GAAWoC,EAAIlB,IAAW7B,EAAaP,EAAS,SAAUe,OACtD0C,EAAOvC,GAAWoC,EAAIlB,GACzB3B,EAAOgD,IAASvC,GAAWoC,EAAIlB,GAAUhB,EAAiBrB,EAAcC,EAASiD,IAAI,KAAU7C,EAAYJ,GAAWkD,EAAK9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAAU3D,EAAQiD,GAAKzB,EAASxB,EAAQiD,cACxNxC,EAAKmD,OAAS5D,EACdyD,IAAShD,EAAKoD,OAAyD,WAAhDjE,GAAKkE,YAAY9D,EAAS,mBAC1CS,EAEW,SAAnBsD,EAAoBvC,EAAOwC,EAAgBC,GAOhC,SAATC,GAAU1C,EAAO2C,OACZ7B,EAAI8B,KACJD,GAAkBE,EAAT/B,EAAIgC,GAChBC,EAAKC,EACLA,EAAKhD,EACLiD,EAAKH,EACLA,EAAKhC,GACK2B,EACVO,GAAMhD,EAENgD,EAAKD,GAAM/C,EAAQ+C,IAAOjC,EAAImC,IAAOH,EAAKG,OAhBzCD,EAAKhD,EACR+C,EAAK/C,EACL8C,EAAKF,KACLK,EAAKH,EACLD,EAAML,GAAkB,GACxBU,EAAiB3C,KAAK4C,IAAI,IAAW,EAANN,SAsBzB,CAACH,OAAAA,GAAQU,MARP,SAARA,QAAgBL,EAAKC,EAAKP,EAAW,EAAIO,EAAIC,EAAKH,EAAK,GAQjCO,YAPR,SAAdA,YAAcC,OACTC,EAAON,EACVO,EAAOT,EACPjC,EAAI8B,YACJU,GAA+B,IAAhBA,GAAsBA,IAAgBN,GAAMN,GAAOY,GAC3DR,IAAOG,GAAeC,EAATpC,EAAImC,EAAuB,GAAKD,GAAMP,EAAWe,GAAQA,MAAWf,EAAW3B,EAAIgC,GAAMS,GAAQ,MAI7G,SAAZE,EAAaC,EAAGC,UACfA,IAAmBD,EAAEE,YAAcF,EAAEC,iBAC9BD,EAAEG,eAAiBH,EAAEG,eAAe,GAAKH,EAE/B,SAAlBI,EAAkBC,OACbZ,EAAM5C,KAAK4C,UAAL5C,KAAYwD,GACrBlB,EAAMtC,KAAKsC,UAALtC,KAAYwD,UACZxD,KAAKyD,IAAIb,IAAQ5C,KAAKyD,IAAInB,GAAOM,EAAMN,EAE3B,SAApBoB,KACCC,GAAgB9F,GAAK+F,KAAKC,UAAUF,gBACnBA,GAAcC,MA7FnB,SAAbE,iBACKF,EAAOD,GAAcC,KACxBG,EAAOH,EAAKI,QAAU,GACtBC,EAAYL,EAAKzE,WACjB+E,EAAUN,EAAKzF,SAChB8F,EAAUxC,WAAVwC,EAAkB9E,IAClB+E,EAAQzC,WAARyC,EAAgB/F,IAChBgB,GAAa8E,EACb9F,GAAW+F,EACX9D,EAAU,iBAAC+D,EAAM1E,UAAUsE,EAAKI,GAAM1E,IAoFCqE,GAE5B,SAAZM,EAAYR,UACX/F,GAAO+F,GAAQhG,KACVyG,IAAgBxG,IAA6B,oBAAdyG,UAA6BA,SAASC,OACzE5E,GAAO7B,OAEPwD,IADAF,GAAOkD,UACOE,gBACdC,GAAQrD,GAAKmD,KACbhG,EAAQ,CAACoB,GAAMyB,GAAME,GAAQmD,IACpB5G,GAAK8C,MAAM+D,MACpBC,GAAW9G,GAAK+F,KAAKgB,SAAW,aAChCC,GAAe,mBAAoBJ,GAAQ,UAAY,QAEvDK,GAAWC,EAASC,QAAUrF,GAAKsF,YAActF,GAAKsF,WAAW,oCAAoCC,QAAU,EAAK,iBAAkBvF,IAAmC,EAA3BwF,UAAUC,gBAAmD,EAA7BD,UAAUE,iBAAwB,EAAI,EACpNC,GAAcP,EAASQ,YAAc,iBAAkBjE,GAAS,4CAAgD,kBAAmBA,GAAkD,kDAAxC,uCAA2FkE,MAAM,KAC9OC,WAAW,kBAAM/F,EAAW,GAAG,KAC/BgE,IACAW,GAAe,GAETA,GAzHT,IAAIxG,GAAMwG,GAAsB1E,GAAMyB,GAAME,GAAQmD,GAAOK,GAAUD,GAAclB,GAAepF,EAAOU,GAAaqG,GAAaX,GAElIjF,EAAW,EACXgG,GAAa,GACbvG,GAAa,GACbhB,GAAW,GACXkE,GAAWsD,KAAKC,IAChBxF,EAAU,iBAAC+D,EAAM1E,UAAUA,GAgB3BoG,EAAc,aACdC,EAAa,YAoBbC,GAAc,CAAC7E,EAAG2E,EAAaG,EAAG,OAAQC,GAAI,OAAQC,GAAI,QAASC,IAAK,QAASC,EAAG,QAASC,GAAI,QAAS7C,EAAG,IAAKrC,GAAI9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAASjC,GAAK2G,SAAS7G,EAAO+B,GAAUL,MAAQxB,GAAK4G,aAAenF,GAAKyE,IAAgBvE,GAAOuE,IAAgBpB,GAAMoB,IAAgB,KAChTrE,GAAY,CAACN,EAAG4E,EAAYE,EAAG,MAAOC,GAAI,MAAOC,GAAI,SAAUC,IAAK,SAAUC,EAAG,SAAUC,GAAI,SAAU7C,EAAG,IAAKgD,GAAIT,GAAa5E,GAAI9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAASjC,GAAK2G,SAASP,GAAY5E,KAAM1B,GAASE,GAAK8G,aAAerF,GAAK0E,IAAexE,GAAOwE,IAAerB,GAAMqB,IAAe,KA+EhUC,GAAYS,GAAKhF,GACjBrC,GAAWC,MAAQ,MAEN2F,sBAKZ2B,KAAA,cAAKC,GACJtC,IAAgBD,EAAUvG,KAASkD,QAAQC,KAAK,wCAChD2C,IAAiBD,QACZkD,EAA6bD,EAA7bC,UAAWC,EAAkbF,EAAlbE,YAAapI,EAAqakI,EAAralI,KAAMoD,EAA+Z8E,EAA/Z9E,OAAQiF,EAAuZH,EAAvZG,WAAYC,EAA2YJ,EAA3YI,SAAU3D,EAAiYuD,EAAjYvD,eAAgB4D,EAAiXL,EAAjXK,OAAQC,EAAyWN,EAAzWM,YAAaC,EAA4VP,EAA5VO,OAAQC,EAAoVR,EAApVQ,WAAYC,EAAwUT,EAAxUS,MAAOC,EAAiUV,EAAjUU,YAAaC,EAAoTX,EAApTW,UAAWC,EAAySZ,EAAzSY,OAAQC,EAAiSb,EAAjSa,QAASC,EAAwRd,EAAxRc,UAAWC,EAA6Qf,EAA7Qe,QAASC,EAAoQhB,EAApQgB,OAAQC,EAA4PjB,EAA5PiB,KAAMC,EAAsPlB,EAAtPkB,OAAQC,EAA8OnB,EAA9OmB,UAAWC,EAAmOpB,EAAnOoB,UAAWC,EAAwNrB,EAAxNqB,SAAUC,EAA8MtB,EAA9MsB,UAAWC,EAAmMvB,EAAnMuB,UAAWC,EAAwLxB,EAAxLwB,QAASC,EAA+KzB,EAA/KyB,WAAYC,EAAmK1B,EAAnK0B,OAAQC,EAA2J3B,EAA3J2B,YAAaC,EAA8I5B,EAA9I4B,aAAcC,EAAgI7B,EAAhI6B,eAAgBC,EAAgH9B,EAAhH8B,aAAcC,EAAkG/B,EAAlG+B,QAASC,EAAyFhC,EAAzFgC,SAAUC,EAA+EjC,EAA/EiC,UAAWC,EAAoElC,EAApEkC,QAASC,EAA2DnC,EAA3DmC,YAAalK,EAA8C+H,EAA9C/H,QAASmK,EAAqCpC,EAArCoC,YAAaC,EAAwBrC,EAAxBqC,SAAUC,EAActC,EAAdsC,WA0Bpa,SAAfC,YAAqBC,GAAc9G,KACpB,SAAf+G,GAAgBjG,EAAGkG,UAAsB7I,GAAK4G,MAAQjE,IAAO+D,IAAWA,EAAO9I,QAAQ+E,EAAEtB,SAAawH,GAAoBC,IAAkC,UAAlBnG,EAAEoG,aAA6BjB,GAAeA,EAAYnF,EAAGkG,GAO9L,SAATlH,SACKqH,EAAKhJ,GAAKiJ,OAASlG,EAAgBkG,IACtCC,EAAKlJ,GAAKmJ,OAASpG,EAAgBoG,IACnCC,EAAW5J,KAAKyD,IAAI+F,IAAO5C,EAC3BiD,EAAW7J,KAAKyD,IAAIiG,IAAO9C,EAC5BoB,IAAa4B,GAAYC,IAAa7B,EAASxH,GAAMgJ,EAAIE,EAAID,GAAQE,IACjEC,IACHlC,GAAyB,EAAdlH,GAAKiJ,QAAc/B,EAAQlH,IACtCmH,GAAUnH,GAAKiJ,OAAS,GAAK9B,EAAOnH,IACpCsH,GAAaA,EAAUtH,IACvByH,GAAezH,GAAKiJ,OAAS,GAAQK,GAAa,GAAO7B,EAAUzH,IACnEsJ,GAAatJ,GAAKiJ,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,GAEjCI,IACHhC,GAAwB,EAAdrH,GAAKmJ,QAAc9B,EAAOrH,IACpCoH,GAAQpH,GAAKmJ,OAAS,GAAK/B,EAAKpH,IAChCuH,GAAaA,EAAUvH,IACvB0H,GAAe1H,GAAKmJ,OAAS,GAAQI,GAAa,GAAO7B,EAAU1H,IACnEuJ,GAAavJ,GAAKmJ,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,IAEjCK,IAASC,MACZ5B,GAAUA,EAAO7H,IACbyJ,KACH1C,EAAO/G,IACPyJ,IAAU,GAEXD,IAAQ,GAETE,MAAYA,IAAS,IAAUjB,GAAcA,EAAWzI,IACpD2J,KACHzB,EAAQlI,IACR2J,IAAU,GAEXC,GAAK,EAEI,SAAVC,GAAWC,EAAGC,EAAGC,GAChBf,GAAOe,IAAUF,EACjBX,GAAOa,IAAUD,EACjB/J,GAAKiK,IAAItI,OAAOmI,GAChB9J,GAAKkK,IAAIvI,OAAOoI,GAChBxD,EAAkBqD,GAAPA,IAAYO,sBAAsBxI,IAAWA,KAEjC,SAAxByI,GAAyBN,EAAGC,GACvBvB,IAAa6B,KAChBrK,GAAKqK,KAAOA,GAAO7K,KAAKyD,IAAI6G,GAAKtK,KAAKyD,IAAI8G,GAAK,IAAM,IACrDL,IAAS,GAEG,MAATW,KACHpB,GAAO,IAAMa,EACb9J,GAAKiK,IAAItI,OAAOmI,GAAG,IAEP,MAATO,KACHlB,GAAO,IAAMY,EACb/J,GAAKkK,IAAIvI,OAAOoI,GAAG,IAEpBxD,EAAkBqD,GAAPA,IAAYO,sBAAsBxI,IAAWA,KAE/C,SAAV2I,GAAU3H,OACLiG,GAAajG,EAAG,QAEhBmH,GADJnH,EAAID,EAAUC,EAAGC,IACP2H,QACTR,EAAIpH,EAAE6H,QACNxB,EAAKc,EAAI9J,GAAK8J,EACdZ,EAAKa,EAAI/J,GAAK+J,EACdU,EAAazK,GAAKyK,WACnBzK,GAAK8J,EAAIA,EACT9J,GAAK+J,EAAIA,GACLU,GAAcjL,KAAKyD,IAAIjD,GAAK0K,OAASZ,IAAMzD,GAAe7G,KAAKyD,IAAIjD,GAAK2K,OAASZ,IAAM1D,KAC1FU,IAAW0C,IAAU,GACrBgB,IAAezK,GAAKyK,YAAa,GACjCL,GAAsBpB,EAAIE,GAC1BuB,GAAc5D,GAAeA,EAAY7G,MAgDzB,SAAlB4K,GAAkBjI,UAAKA,EAAEkI,SAA8B,EAAnBlI,EAAEkI,QAAQzJ,SAAepB,GAAK8K,aAAc,IAAS9C,EAAerF,EAAG3C,GAAKyK,YAChG,SAAhBM,YAAuB/K,GAAK8K,aAAc,IAAU7C,EAAajI,IACtD,SAAXgL,GAAWrI,OACNiG,GAAajG,QACbmH,EAAImB,KACPlB,EAAImB,KACLrB,IAASC,EAAIqB,IAAW7C,GAAcyB,EAAIqB,IAAW9C,EAAa,GAClE6C,GAAUrB,EACVsB,GAAUrB,EACVvD,GAAU6E,GAAkBC,SAAQ,IAE1B,SAAXC,GAAW5I,OACNiG,GAAajG,IACjBA,EAAID,EAAUC,EAAGC,GACjBsF,IAAYyB,IAAU,OAClB6B,GAA8B,IAAhB7I,EAAE8I,UAAkBnF,EAA6B,IAAhB3D,EAAE8I,UAAkBtM,GAAKuM,YAAc,GAAK/E,EAC/FkD,GAAQlH,EAAEsG,OAASuC,EAAY7I,EAAEwG,OAASqC,EAAY,GACtDhF,IAAWuB,GAAgBsD,GAAkBC,SAAQ,IAE5C,SAAVK,GAAUhJ,OACLiG,GAAajG,QACbmH,EAAInH,EAAE4H,QACTR,EAAIpH,EAAE6H,QACNxB,EAAKc,EAAI9J,GAAK8J,EACdZ,EAAKa,EAAI/J,GAAK+J,EACf/J,GAAK8J,EAAIA,EACT9J,GAAK+J,EAAIA,EACTP,IAAQ,EACRhD,GAAU6E,GAAkBC,SAAQ,IACnCtC,GAAME,IAAOkB,GAAsBpB,EAAIE,IAE9B,SAAX0C,GAAWjJ,GAAM3C,GAAK4G,MAAQjE,EAAGgF,EAAQ3H,IAC3B,SAAd6L,GAAclJ,GAAM3C,GAAK4G,MAAQjE,EAAGiF,EAAW5H,IACpC,SAAX8L,GAAWnJ,UAAKiG,GAAajG,IAAOD,EAAUC,EAAGC,IAAmByF,EAAQrI,SA3LxEqB,OAASA,EAASvB,EAAWuB,IAAWP,QACxCqF,KAAOA,EACDO,EAAXA,GAAoBrJ,GAAK8C,MAAMC,QAAQsG,GACvCN,EAAYA,GAAa,KACzBC,EAAcA,GAAe,EAC7BM,EAAaA,GAAc,EAC3B2B,EAAcA,GAAe,EAC7BrK,EAAOA,GAAQ,sBACfsI,GAAwB,IAAbA,EACID,EAAfA,GAA4ByF,WAAW5M,GAAK6M,iBAAiB/H,IAAOqC,aAAe,OAC/EsD,GAAIyB,GAAmB5B,GAASD,GAAOG,GAASD,GAAQW,GAC3DrK,GAAOiM,KACP3C,GAAa,EACbC,GAAa,EACbpL,GAAUgI,EAAKhI,UAAYyE,EAC3BqI,GAAcxK,EAAeY,EAAQkE,IACrC2F,GAAczK,EAAeY,EAAQL,IACrCmK,GAAUF,KACVG,GAAUF,KACVpC,IAAgB7K,EAAKL,QAAQ,YAAcK,EAAKL,QAAQ,YAAiC,gBAAnBkH,GAAY,GAClFoH,GAAarO,EAAYwD,GACzB8K,GAAW9K,EAAO+K,eAAiBxL,GACnCqI,GAAS,CAAC,EAAG,EAAG,GAChBE,GAAS,CAAC,EAAG,EAAG,GAChBR,GAAc,EAqFd0D,GAAWrM,GAAKgH,QAAU,SAAArE,GACrBiG,GAAajG,EAAG,IAAOA,GAAKA,EAAE2J,SAClCtM,GAAKqK,KAAOA,GAAO,KACnBgB,GAAkBkB,QAClBvM,GAAKtB,WAAY,EACjBiE,EAAID,EAAUC,GACd2G,GAAaC,GAAa,EAC1BvJ,GAAK0K,OAAS1K,GAAK8J,EAAInH,EAAE4H,QACzBvK,GAAK2K,OAAS3K,GAAK+J,EAAIpH,EAAE6H,QACzBxK,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACTrE,EAAa+J,EAAe1G,EAAS8K,GAAUrH,GAAY,GAAIwF,GAASnM,IAAS,GACjF6B,GAAKiJ,OAASjJ,GAAKmJ,OAAS,EAC5BnC,GAAWA,EAAQhH,MAEpBwM,GAAaxM,GAAKiH,UAAY,SAAAtE,OACzBiG,GAAajG,EAAG,IACpBrE,EAAgByJ,EAAe1G,EAAS8K,GAAUrH,GAAY,GAAIwF,IAAS,OACvEmC,GAAkBC,MAAM1M,GAAK+J,EAAI/J,GAAK2K,QACzCgC,EAAc3M,GAAKyK,WACnBmC,EAAiBD,IAAiD,EAAjCnN,KAAKyD,IAAIjD,GAAK8J,EAAI9J,GAAK0K,SAAgD,EAAjClL,KAAKyD,IAAIjD,GAAK+J,EAAI/J,GAAK2K,SAC9FkC,EAAYnK,EAAUC,IAClBiK,GAAkBH,IACtBzM,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QAELO,GAAkB2F,GACrBlL,GAAKyP,YAAY,IAAM,cACS,IAA3BjL,KAAa8G,KAAsBhG,EAAEoK,oBACpCpK,EAAEtB,OAAO2L,MACZrK,EAAEtB,OAAO2L,aACH,GAAIb,GAASc,YAAa,KAC5BC,EAAiBf,GAASc,YAAY,eAC1CC,EAAeC,eAAe,SAAS,GAAM,EAAMhO,GAAM,EAAG0N,EAAUO,QAASP,EAAUQ,QAASR,EAAUtC,QAASsC,EAAUrC,SAAS,GAAO,GAAO,GAAO,EAAO,EAAG,MACvK7H,EAAEtB,OAAOiM,cAAcJ,OAM5BlN,GAAKyK,WAAazK,GAAK8K,YAAc9K,GAAKtB,WAAY,EACtD8H,GAAUmG,IAAgB5E,GAAgBsD,GAAkBC,SAAQ,GACpExE,GAAa6F,GAAe7F,EAAU9G,IACtCiH,GAAaA,EAAUjH,GAAM4M,KAqC/BvB,GAAoBrL,GAAKuN,IAAMlQ,GAAKyP,YAAYrG,GAAe,IAlKjD,SAAb+G,aACCxN,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACTgJ,GAAkBkB,QAClB/F,GAAUA,EAAOxG,MA8J8DuM,QAEjFvM,GAAKiJ,OAASjJ,GAAKmJ,OAAS,EAC5BnJ,GAAKiK,IAAMzI,EAAiB,EAAG,IAAI,GACnCxB,GAAKkK,IAAM1I,EAAiB,EAAG,IAAI,GACnCxB,GAAKmL,QAAUF,GACfjL,GAAKoL,QAAUF,GACflL,GAAKyK,WAAazK,GAAK8K,YAAc9K,GAAKtB,WAAY,EACtDyF,GAAS8H,MACTjM,GAAKyN,OAAS,SAAA9K,UACR3C,GAAK0N,YACT1P,EAAakO,GAAaC,GAAW9K,EAAQ,SAAU7C,GAC7B,GAA1BP,EAAKL,QAAQ,WAAkBI,EAAakO,GAAaC,GAAW9K,EAAQ,SAAU2J,GAAU7M,GAASC,GAChF,GAAzBH,EAAKL,QAAQ,UAAiBI,EAAaqD,EAAQ,QAASkK,GAAUpN,GAASC,IACjD,GAAzBH,EAAKL,QAAQ,UAAiB0G,IAAwC,GAA3BrG,EAAKL,QAAQ,cAC5DI,EAAaqD,EAAQyD,GAAY,GAAIuH,GAAUlO,GAASC,GACxDJ,EAAamO,GAAUrH,GAAY,GAAI0H,IACvCxO,EAAamO,GAAUrH,GAAY,GAAI0H,IACvCjE,GAAevK,EAAaqD,EAAQ,QAASqH,IAAc,GAAM,GACjEL,GAAWrK,EAAaqD,EAAQ,QAASyK,IACzC9D,GAAkBhK,EAAamO,GAAU,eAAgBvB,IACzD3C,GAAgBjK,EAAamO,GAAU,aAAcpB,IACrDpD,GAAW3J,EAAaqD,EAAQgD,GAAe,QAASuH,IACxDhE,GAAc5J,EAAaqD,EAAQgD,GAAe,QAASwH,IAC3DhE,GAAU7J,EAAaqD,EAAQgD,GAAe,OAAQsH,KAEvD3L,GAAK0N,WAAY,EACjB/K,GAAKA,EAAE1E,MAAQoO,GAAS1J,GACxBwF,GAAYA,EAASnI,KAEfA,IAERA,GAAK2N,QAAU,WACV3N,GAAK0N,YAERxI,GAAW0I,OAAO,SAAAC,UAAKA,IAAM7N,IAAQnC,EAAYgQ,EAAExM,UAASD,QAAU9C,EAAgB4N,GAAaC,GAAW9K,EAAQ,SAAU7C,GAC5HwB,GAAKtB,YACRsB,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACT/D,EAAgByJ,EAAe1G,EAAS8K,GAAUrH,GAAY,GAAIwF,IAAS,IAE5EhM,EAAgB4N,GAAaC,GAAW9K,EAAQ,SAAU2J,GAAU5M,GACpEE,EAAgB+C,EAAQ,QAASkK,GAAUnN,GAC3CE,EAAgB+C,EAAQyD,GAAY,GAAIuH,GAAUjO,GAClDE,EAAgB6N,GAAUrH,GAAY,GAAI0H,IAC1ClO,EAAgB6N,GAAUrH,GAAY,GAAI0H,IAC1ClO,EAAgB+C,EAAQ,QAASqH,IAAc,GAC/CpK,EAAgB+C,EAAQ,QAASyK,IACjCxN,EAAgB6N,GAAU,eAAgBvB,IAC1CtM,EAAgB6N,GAAU,aAAcpB,IACxCzM,EAAgB+C,EAAQgD,GAAe,QAASuH,IAChDtN,EAAgB+C,EAAQgD,GAAe,QAASwH,IAChDvN,EAAgB+C,EAAQgD,GAAe,OAAQsH,IAC/C3L,GAAK0N,UAAY1N,GAAKtB,UAAYsB,GAAKyK,YAAa,EACpDrC,GAAaA,EAAUpI,MAIzBA,GAAK8N,KAAO9N,GAAK+N,OAAS,WACzB/N,GAAK2N,cACD5M,EAAImE,GAAWtH,QAAQoC,IACtB,GAALe,GAAUmE,GAAW8I,OAAOjN,EAAG,GAC/BtC,KAAgBuB,KAASvB,GAAc,IAGxCyG,GAAWjE,KAAKjB,IAChB+H,GAAgBlK,EAAYwD,KAAY5C,GAAcuB,IAEtDA,GAAKyN,OAAO7G,8JAILqF,KAAKhC,IAAI3H,2DAGT2J,KAAK/B,IAAI5H,8CAhRL6D,QACND,KAAKC,GAoRZ5B,EAAS0J,QAAU,SACnB1J,EAAS2J,OAAS,SAAA/H,UAAQ,IAAI5B,EAAS4B,IACvC5B,EAAS4J,SAAWvK,EACpBW,EAAS6J,OAAS,kBAAMlJ,GAAWmJ,SACnC9J,EAAS+J,QAAU,SAAA1E,UAAM1E,GAAW0I,OAAO,SAAAC,UAAKA,EAAE1H,KAAKyD,KAAOA,IAAI,IAElExM,KAAcC,GAAKE,eAAegH,GClZnB,SAAdgK,GAAetP,EAAOhB,EAAM+B,OACvBkE,EAASsK,GAAUvP,KAAkC,WAAvBA,EAAMwP,OAAO,EAAG,KAA2C,EAAxBxP,EAAMrB,QAAQ,eACnFoC,EAAK,IAAM/B,EAAO,SAAWiG,GACdjF,EAAMwP,OAAO,EAAGxP,EAAMmC,OAAS,GAAKnC,EAEvC,SAAbyP,GAAczP,EAAOiF,UAAUA,GAAWsK,GAAUvP,IAAiC,WAAvBA,EAAMwP,OAAO,EAAG,GAA4CxP,EAAzB,SAAWA,EAAQ,IAE9F,SAAtB0P,YAA4BC,GAAiB,EACzB,SAApBC,YAA0BD,GAAiB,EAC5B,SAAfE,GAAevP,UAAKA,EACX,SAATwP,GAAS9P,UAASO,KAAKC,MAAc,IAARR,GAAkB,KAAU,EACzC,SAAhB+P,WAAyC,oBAAZ1R,OAClB,SAAXF,YAAiBC,IAAS2R,OAAoB3R,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAC9E,SAAdQ,GAAc8E,YAAQ5E,EAAMH,QAAQ+E,GACZ,SAAxBsM,GAAwBC,UAA4C,WAAtBA,EAAiCC,EAAShQ,GAAK,QAAU+P,KAAuBpO,GAAO,SAAWoO,IAAsBjL,GAAM,SAAWiL,GACtK,SAAjBE,GAAiB3R,UAAWD,EAAcC,EAAS,2BAA6BI,GAAYJ,GAAW,kBAAO4R,GAAYC,MAAQnQ,GAAKoQ,WAAYF,GAAYG,OAASL,EAAeE,IAAgB,kBAAMI,GAAWhS,KAG3M,SAAbiS,GAAcjS,SAAUiD,IAAAA,EAAGmF,IAAAA,GAAID,IAAAA,EAAG5C,IAAAA,SAAOxD,KAAK4C,IAAI,GAAI1B,EAAI,SAAWmF,KAAQ7C,EAAIxF,EAAcC,EAASiD,IAAMsC,IAAMoM,GAAe3R,EAAf2R,GAA0BxJ,GAAK/H,GAAYJ,IAAYqD,GAAOJ,IAAMuD,GAAMvD,IAAMuO,GAAsBpJ,GAAMpI,EAAQiD,GAAKjD,EAAQ,SAAWoI,IAC1O,SAAtB8J,GAAuBzR,EAAM0R,OACvB,IAAI7O,EAAI,EAAGA,EAAI8O,EAAazO,OAAQL,GAAK,EAC3C6O,KAAWA,EAAOhS,QAAQiS,EAAa9O,EAAE,KAAQ7C,EAAK2R,EAAa9O,GAAI8O,EAAa9O,EAAE,GAAI8O,EAAa9O,EAAE,IAI/F,SAAd+O,GAAc7Q,SAA2B,mBAAXA,EAClB,SAAZ8Q,GAAY9Q,SAA2B,iBAAXA,EAChB,SAAZ+Q,GAAY/Q,SAA2B,iBAAXA,EACZ,SAAhBgR,GAAiBC,EAAWC,EAAU5D,UAAU2D,GAAaA,EAAUE,SAASD,EAAW,EAAI,IAAM5D,GAAS2D,EAAU3D,QAC5G,SAAZ8D,GAAarQ,EAAM9B,MACd8B,EAAKsQ,QAAS,KACbC,EAASvQ,EAAKC,KAAOD,EAAKC,KAAKuQ,IAAI,kBAAMtS,EAAK8B,KAAS9B,EAAK8B,GAChEuQ,GAAUA,EAAOE,YAAczQ,EAAK0Q,kBAAoBH,IAmBtC,SAApBI,GAAoBlT,UAAW0B,GAAK6M,iBAAiBvO,GAKtC,SAAfmT,GAAgBC,EAAKC,OACf,IAAItL,KAAKsL,EACZtL,KAAKqL,IAASA,EAAIrL,GAAKsL,EAAStL,WAE3BqL,EAQG,SAAXE,GAAYtT,SAAUoI,IAAAA,UAAQpI,EAAQ,SAAWoI,IAAOpI,EAAQ,SAAWoI,IAAO,EAC5D,SAAtBmL,GAAsBC,OAIpBzL,EAHGxC,EAAI,GACPkO,EAASD,EAASC,OAClBC,EAAWF,EAASE,eAEhB3L,KAAK0L,EACTlO,EAAE/B,KAAKiQ,EAAO1L,GAAK2L,UAEbnO,EAGW,SAAnBoO,GAAmBC,OACdC,EAAOjU,GAAK8C,MAAMmR,KAAKD,GAC1BrO,EAAIuO,MAAMC,QAAQH,IAAyBA,EAAqBhD,MAAM,GAAGoD,KAAK,SAACzO,EAAG0O,UAAM1O,EAAI0O,WACtF1O,EAAI,SAAC/D,EAAO0S,EAAWC,OACzB7Q,cADyB6Q,IAAAA,EAAW,OAEnCD,SACGL,EAAKrS,MAEG,EAAZ0S,EAAe,KAClB1S,GAAS2S,EACJ7Q,EAAI,EAAGA,EAAIiC,EAAE5B,OAAQL,OACrBiC,EAAEjC,IAAM9B,SACJ+D,EAAEjC,UAGJiC,EAAEjC,EAAE,OAEXA,EAAIiC,EAAE5B,OACNnC,GAAS2S,EACF7Q,QACFiC,EAAEjC,IAAM9B,SACJ+D,EAAEjC,UAILiC,EAAE,IACN,SAAC/D,EAAO0S,EAAWC,YAAAA,IAAAA,EAAW,UAC7BC,EAAUP,EAAKrS,UACX0S,GAAanS,KAAKyD,IAAI4O,EAAU5S,GAAS2S,GAAeC,EAAU5S,EAAQ,GAAO0S,EAAY,EAAKE,EAAUP,EAAKK,EAAY,EAAI1S,EAAQoS,EAAuBpS,EAAQoS,IAIjK,SAAjBS,GAAkB5T,EAAMT,EAASsU,EAAOC,UAAaD,EAAM/M,MAAM,KAAKiN,QAAQ,SAAAhU,UAAQC,EAAKT,EAASQ,EAAM+T,KAC3F,SAAfhU,GAAgBP,EAASQ,EAAMC,EAAMgU,EAAY9T,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACC,SAAU+T,EAAY9T,UAAWA,IAClH,SAAlBE,GAAmBb,EAASQ,EAAMC,EAAME,UAAYX,EAAQc,oBAAoBN,EAAMC,IAAQE,GAC7E,SAAjB+T,GAAkBjU,EAAMJ,EAAIsU,IAC3BA,EAAaA,GAAcA,EAAWC,gBAErCnU,EAAKJ,EAAI,QAASsU,GAClBlU,EAAKJ,EAAI,YAAasU,IAMV,SAAdE,GAAerT,EAAOsT,MACjB/D,GAAUvP,GAAQ,KACjBuT,EAAUvT,EAAMrB,QAAQ,KAC3B6U,GAAYD,GAAYvT,EAAMyT,OAAOF,EAAQ,GAAK,GAAKzG,WAAW9M,EAAMwP,OAAO+D,EAAU,IAAM,GAC3FA,IACHvT,EAAMrB,QAAQ,KAAO4U,IAAaC,GAAYF,EAAO,KACtDtT,EAAQA,EAAMwP,OAAO,EAAG+D,EAAQ,IAEjCvT,EAAQwT,GAAaxT,KAAS0T,EAAaA,EAAU1T,GAASsT,GAAQtT,EAAMrB,QAAQ,KAAOmO,WAAW9M,GAASsT,EAAO,IAAMxG,WAAW9M,IAAU,UAE3IA,EAEQ,SAAhB2T,GAAiB3U,EAAM0F,EAAMkP,EAAWlB,IAAiE9R,EAAQiT,EAAcC,OAA3EC,IAAAA,WAAYC,IAAAA,SAAUC,IAAAA,SAAUC,IAAAA,OAAQC,IAAAA,WACvFzQ,EAAI/B,GAAKyS,cAAc,OAC1BC,EAAmBzV,GAAYgV,IAAsD,UAAxCrV,EAAcqV,EAAW,WACtEU,GAA2C,IAA9BtV,EAAKL,QAAQ,YAC1B4V,EAASF,EAAmBrP,GAAQ4O,EACpCY,GAAqC,IAA3BxV,EAAKL,QAAQ,SACvB8V,EAAQD,EAAUT,EAAaC,EAC/BU,EAAM,gBAAkBD,EAAQ,cAAgBR,EAAW,UAAYQ,EAAQ,gBAAkBN,EAAa,8IAC/GO,GAAO,cAAgBJ,GAAcR,IAAuBO,EAAmB,SAAW,cACzFC,IAAcR,GAAuBO,IAAsBK,IAAQhC,IAAc3Q,GAAY4S,EAASC,GAAW,KAAOhU,EAASkM,WAAWoH,IAAW,OACxJL,IAAiBa,GAAO,+CAAiDb,EAAagB,YAAc,OACpGnR,EAAEoR,SAAWN,EACb9Q,EAAEqR,aAAa,QAAS,eAAiB/V,GAAQ0F,EAAO,WAAaA,EAAO,KAC5EhB,EAAEsR,MAAMC,QAAUP,EAClBhR,EAAEwR,UAAYxQ,GAAiB,IAATA,EAAa1F,EAAO,IAAM0F,EAAO1F,EACvDuV,EAAOY,SAAS,GAAKZ,EAAOa,aAAa1R,EAAG6Q,EAAOY,SAAS,IAAMZ,EAAOc,YAAY3R,GACrFA,EAAE4R,QAAU5R,EAAE,SAAWgP,EAAU3L,GAAGH,IACtC2O,EAAgB7R,EAAG,EAAGgP,EAAW8B,GAC1B9Q,EAiBA,SAAR8R,YAA6C,GAA/B5S,KAAa6S,KAAoCC,EAAXA,GAAoBxK,sBAAsByK,IAClF,SAAZpW,KACMC,GAAgBA,EAAYC,aAAaD,EAAYiM,OAASzG,GAAM4Q,eACxElW,GAAWC,QACPH,EACQkW,EAAXA,GAAoBxK,sBAAsByK,GAE1CA,IAEDF,IAAmBI,EAAU,eAC7BJ,GAAkB7S,MAGC,SAArBkT,KACCC,EAAmB7V,GAAKoQ,WACxB0F,EAAoB9V,GAAKuM,YAEd,SAAZwJ,KACCvW,GAAWC,QACVuW,IAAgBC,GAAkBxU,GAAKyU,mBAAsBzU,GAAK0U,yBAA6BC,GAAuBP,IAAqB7V,GAAKoQ,cAAc/P,KAAKyD,IAAI9D,GAAKuM,YAAcuJ,GAAwC,IAAnB9V,GAAKuM,cAAuB8J,EAAalK,SAAQ,GAInP,SAAfmK,YAAqBnX,GAAgB6E,GAAe,YAAasS,KAAiBC,IAAY,GAG5E,SAAlBC,GAAkBC,OACZ,IAAI7U,EAAI,EAAGA,EAAI8U,EAAazU,OAAQL,GAAG,IACtC6U,GAASC,EAAa9U,EAAE,IAAM8U,EAAa9U,EAAE,GAAG+U,QAAUF,KAC9DC,EAAa9U,GAAGkT,MAAMC,QAAU2B,EAAa9U,EAAE,GAC/C8U,EAAa9U,GAAGgV,SAAWF,EAAa9U,GAAGiT,aAAa,YAAa6B,EAAa9U,EAAE,IAAM,IAC1F8U,EAAa9U,EAAE,GAAGiV,QAAU,GAIlB,SAAbC,GAAcnI,EAAM8H,OACfM,MACCC,GAAK,EAAGA,GAAKC,GAAUhV,OAAQ+U,OACnCD,EAAUE,GAAUD,MACHP,GAASM,EAAQjW,OAAS2V,IACtC9H,EACHoI,EAAQpI,KAAK,GAEboI,EAAQnI,QAAO,GAAM,IAIxBsI,GAAc,EACdT,GAASD,GAAgBC,GACzBA,GAASd,EAAU,UAEC,SAArBwB,GAAsBjX,EAAmBuC,GACxCjD,GAAWC,SACVgD,GAAU2U,IAAmB5X,GAAWsT,QAAQ,SAAApB,UAAOf,GAAYe,IAAQA,EAAIlR,YAAckR,EAAI2F,IAAM,KACxGhI,GAAUnP,KAAuBF,GAAKC,QAAQC,kBAAoBoX,EAAqBpX,GAWxE,SAAhBqX,KACCzS,GAAMqQ,YAAYqC,GAClBxH,GAAW1Q,GAAekY,EAAUC,cAAiBzX,GAAKuM,YAC1DzH,GAAM4S,YAAYF,GAED,SAAlBG,GAAkBC,UAAQC,GAAS,gGAAgG/E,QAAQ,SAAAnU,UAAMA,EAAGmW,MAAMgD,QAAUF,EAAO,OAAS,UA2GvK,SAAbG,GAAcC,EAAKC,EAAQC,EAAIC,OACzBH,EAAII,MAAMC,UAAW,SAIxBhS,EAHGzE,EAAI0W,EAAiBrW,OACxBsW,EAAcN,EAAOnD,MACrB0D,EAAWR,EAAIlD,MAETlT,KAEN2W,EADAlS,EAAIiS,EAAiB1W,IACJsW,EAAG7R,GAErBkS,EAAYE,SAA2B,aAAhBP,EAAGO,SAA0B,WAAa,WACjD,WAAfP,EAAGJ,UAA0BS,EAAYT,QAAU,gBACpDU,EAAS9D,GAAW8D,EAAS/D,GAAU,OACvC8D,EAAYG,UAAYR,EAAGQ,WAAa,OACxCH,EAAYI,SAAW,UACvBJ,EAAYK,UAAY,aACxBL,EAAYM,IAAUjH,GAASoG,EAAK5R,IAAe0S,GACnDP,EAAYQ,IAAWnH,GAASoG,EAAKnW,IAAaiX,GAClDP,EAAYS,IAAYR,EAASS,IAAWT,EAAQ,IAASA,EAAQ,KAAU,IAC/EU,GAAUf,GACVK,EAASK,IAAUL,EAAQ,SAAmBN,EAAGW,IACjDL,EAASO,IAAWP,EAAQ,UAAoBN,EAAGa,IACnDP,EAASQ,IAAYd,EAAGc,IACpBhB,EAAImB,aAAelB,IACtBD,EAAImB,WAAWjE,aAAa+C,EAAQD,GACpCC,EAAO9C,YAAY6C,IAEpBA,EAAII,MAAMC,WAAY,GAsBZ,SAAZe,GAAY9a,WACP+a,EAAIC,GAAYrX,OACnB6S,EAAQxW,EAAQwW,MAChByE,EAAQ,GACR3X,EAAI,EACEA,EAAIyX,EAAGzX,IACb2X,EAAMzX,KAAKwX,GAAY1X,GAAIkT,EAAMwE,GAAY1X,YAE9C2X,EAAM3Y,EAAItC,EACHib,EAuBS,SAAjBC,GAAkB1Z,EAAOiX,EAAS0C,EAAcjH,EAAWkH,EAAQC,EAAQC,EAAgB/Y,EAAMgZ,EAAgBC,EAAa3F,EAAkB4F,EAAanG,EAAoBoG,GAChLrJ,GAAY7Q,KAAWA,EAAQA,EAAMe,IACjCwO,GAAUvP,IAAgC,QAAtBA,EAAMwP,OAAO,EAAE,KACtCxP,EAAQia,GAAmC,MAApBja,EAAMyT,OAAO,GAAaJ,GAAY,IAAMrT,EAAMwP,OAAO,GAAImK,GAAgB,QAGpGQ,EAAI3T,EAAIhI,EADL4b,EAAOtG,EAAqBA,EAAmBsG,OAAS,KAE5DtG,GAAsBA,EAAmBuG,KAAK,GAC9C5M,MAAMzN,KAAWA,GAASA,GACrB8Q,GAAU9Q,GAkBd8T,IAAuB9T,EAAQ5B,GAAK8C,MAAMoZ,SAASxG,EAAmByG,cAAcC,MAAO1G,EAAmByG,cAAcE,IAAK,EAAGR,EAAaja,IACjJ8Z,GAAkBvE,EAAgBuE,EAAgBH,EAAcjH,GAAW,OAnBrD,CACtB7B,GAAYoG,KAAaA,EAAUA,EAAQlW,QAE1C2Z,EAAQC,EAAaC,EAAc5C,EADhC6C,GAAW7a,GAAS,KAAK+F,MAAM,KAEnCvH,EAAUqC,EAAWoW,EAASlW,IAASiE,IACvC0V,EAASlK,GAAWhS,IAAY,MACdkc,EAAOI,MAASJ,EAAOK,MAAgD,SAAvCrJ,GAAkBlT,GAASwZ,UAC5EA,EAAUxZ,EAAQwW,MAAMgD,QACxBxZ,EAAQwW,MAAMgD,QAAU,QACxB0C,EAASlK,GAAWhS,GACpBwZ,EAAWxZ,EAAQwW,MAAMgD,QAAUA,EAAWxZ,EAAQwW,MAAMgG,eAAe,YAE5EL,EAActH,GAAYwH,EAAQ,GAAIH,EAAOhI,EAAU/L,IACvDiU,EAAevH,GAAYwH,EAAQ,IAAM,IAAKlB,GAC9C3Z,EAAQ0a,EAAOhI,EAAUnM,GAAKwT,EAAerH,EAAUnM,GAAKyT,EAAcW,EAAcf,EAASgB,EACjGd,GAAkBvE,EAAgBuE,EAAgBc,EAAclI,EAAYiH,EAAeiB,EAAe,IAAOd,EAAehF,UAA2B,GAAf8F,GAC5IjB,GAAgBA,EAAeiB,KAK5BV,IACHnZ,EAAKmZ,GAAiBla,IAAU,KAChCA,EAAQ,IAAMA,EAAQ,IAEnB6Z,EAAQ,KACPlB,EAAW3Y,EAAQ2Z,EACtBnF,EAAUqF,EAAO/E,SAClBqF,EAAK,SAAWzH,EAAU9L,GAC1B2O,EAAgBsE,EAAQlB,EAAUjG,EAAY8B,GAAsB,GAAXmE,IAAoBnE,IAAYH,EAAmB9T,KAAK4C,IAAI6B,GAAMmV,GAAKtY,GAAOsY,IAAON,EAAOR,WAAWc,KAAQxB,EAAW,GAC/KtE,IACH0F,EAAiBvJ,GAAWsJ,GAC5BzF,IAAqBwF,EAAO7E,MAAMtC,EAAU3L,GAAGR,GAAMwT,EAAerH,EAAU3L,GAAGR,GAAKmM,EAAU3L,GAAGkU,EAAIpB,EAAOvE,QAAW0D,YAGvHlF,GAAsBtV,IACzB2b,EAAK3J,GAAWhS,GAChBsV,EAAmBuG,KAAKJ,GACxBzT,EAAKgK,GAAWhS,GAChBsV,EAAmBoH,cAAgBf,EAAGzH,EAAUnM,GAAKC,EAAGkM,EAAUnM,GAClEvG,EAAQA,EAAS8T,EAAmBoH,cAAiBjB,GAEtDnG,GAAsBA,EAAmBuG,KAAKD,GACvCtG,EAAqB9T,EAAQO,KAAKC,MAAMR,GAGpC,SAAZmb,GAAa3c,EAAS+V,EAAQwG,EAAKD,MAC9Btc,EAAQ6a,aAAe9E,EAAQ,KAEjChO,EAAG6R,EADApD,EAAQxW,EAAQwW,SAEhBT,IAAWvP,GAAO,KAGhBuB,KAFL/H,EAAQ4c,QAAUpG,EAAMC,QACxBmD,EAAK1G,GAAkBlT,IAEhB+H,GAAM8U,GAAWC,KAAK/U,KAAM6R,EAAG7R,IAA0B,iBAAbyO,EAAMzO,IAAyB,MAANA,IAC1EyO,EAAMzO,GAAK6R,EAAG7R,IAGhByO,EAAM+F,IAAMA,EACZ/F,EAAM8F,KAAOA,OAEb9F,EAAMC,QAAUzW,EAAQ4c,QAEzBhd,GAAK+F,KAAKoX,SAAS/c,GAASuY,QAAU,EACtCxC,EAAOc,YAAY7W,IAGE,SAAvBgd,GAAwBC,EAAcC,EAAcC,OAC/CC,EAAQF,EACXG,EAAQD,SACF,SAAA5b,OACF8b,EAAUvb,KAAKC,MAAMib,YACrBK,IAAYF,GAASE,IAAYD,GAAqC,EAA5Btb,KAAKyD,IAAI8X,EAAUF,IAA0C,EAA5Brb,KAAKyD,IAAI8X,EAAUD,KACjG7b,EAAQ8b,EACRH,GAAeA,KAEhBE,EAAQD,EACRA,EAAQ5b,GAIK,SAAf+b,GAAgBlC,EAAQnH,EAAW1S,OAC9BkH,EAAO,GACXA,EAAKwL,EAAUnM,GAAK,KAAOvG,EAC3B5B,GAAK4d,IAAInC,EAAQ3S,GAUC,SAAnB+U,GAAoBC,EAAUxJ,GAGjB,SAAXyJ,GAAYtV,EAAUK,EAAMwU,EAAcU,EAASC,OAC9CC,EAAQH,GAASG,MACpBC,EAAarV,EAAKqV,WAClBC,EAAY,GACbd,EAAeA,GAAgBe,QAC3BC,EAAuBlB,GAAqBiB,EAAWf,EAAc,WACxEY,EAAMzN,OACNsN,GAASG,MAAQ,WAElBD,EAAWD,GAAWC,GAAY,EAClCD,EAAUA,GAAYvV,EAAW6U,EACjCY,GAASA,EAAMzN,OACf3H,EAAKyV,GAAQ9V,EACbK,EAAK0V,SAAU,GACf1V,EAAKsV,UAAYA,GACPG,GAAQ,kBAAMD,EAAqBhB,EAAeU,EAAUE,EAAMO,MAAQR,EAAUC,EAAMO,MAAQP,EAAMO,QAClH3V,EAAK4V,SAAW,WACfpd,GAAWC,QACXwc,GAASG,OAAS3G,KAEnBzO,EAAKqV,WAAa,WACjBJ,GAASG,MAAQ,EACjBC,GAAcA,EAAWQ,KAAKT,IAE/BA,EAAQH,GAASG,MAAQle,GAAK4e,GAAGd,EAAUhV,OA1BzCuV,EAAYjb,EAAe0a,EAAUxJ,GACxCiK,EAAO,UAAYjK,EAAUlM,UA4B9B0V,EAASS,GAAQF,GACPrJ,aAAe,kBAAM+I,GAASG,OAASH,GAASG,MAAMzN,SAAWsN,GAASG,MAAQ,IAC5Fvd,GAAamd,EAAU,QAASO,EAAUrJ,cAC1ClP,GAAcqB,SAAWxG,GAAamd,EAAU,YAAaO,EAAUrJ,cAChE+I,GA9jBT,IAAI/d,GAAMwG,EAAc1E,GAAMyB,GAAME,GAAQmD,GAAOlG,EAAOyX,EAAcwB,GAAUkF,GAAQC,GAAQC,EAAejH,GAAavG,GAAgByN,EAAgBlG,GAAImG,EAAYC,EAAa1M,EAAc2M,GAAOC,GAAqBrH,EAAe3W,EAAa8W,EAAqBN,EAAmBD,EAAkB0H,EAAYvY,EAAUsS,EAAoBE,EAAWxH,EAAQkH,EAAasG,GACpYC,GAiLAjI,EAyDA4B,GAEAsG,GAqEAC,GAhTA5d,GAAW,EACX2C,GAAWsD,KAAKC,IAChB2X,EAASlb,KACT6S,GAAkB,EAClBsI,GAAW,EAyBXxO,GAAY,SAAZA,UAAYvP,SAA2B,iBAAXA,GAW5Bge,GAAOzd,KAAKyD,IAGZ2Q,EAAS,QACTC,EAAU,SACVmE,GAAS,QACTE,GAAU,SACVgF,GAAS,QACTC,GAAQ,OACRC,GAAO,MACPC,GAAU,SACVlF,GAAW,UACXC,GAAU,SACVkF,GAAS,QACTC,EAAU,SACVtF,GAAM,KAYNxI,GAAa,SAAbA,WAAchS,EAAS+f,OAClBjC,EAAQiC,GAAoE,6BAA/C7M,GAAkBlT,GAAS4e,IAAkDhf,GAAK4e,GAAGxe,EAAS,CAACqM,EAAG,EAAGC,EAAG,EAAG0T,SAAU,EAAGC,SAAU,EAAGC,SAAU,EAAGC,UAAW,EAAGC,UAAW,EAAGC,MAAO,EAAGC,MAAO,EAAGC,MAAO,IAAI5N,SAAS,GACtPuJ,EAASlc,EAAQwgB,+BAClB1C,GAASA,EAAMnL,SAAS,GAAGtC,OACpB6L,GAwDRuE,GAAkB,CAAClL,WAAY,QAASC,SAAU,MAAOE,OAAQ,EAAGD,SAAU,OAAQE,WAAW,UACjG+K,GAAY,CAACC,cAAe,OAAQC,cAAe,GACnD1L,EAAY,CAACqH,IAAK,EAAGD,KAAM,EAAGuE,OAAQ,GAAKC,OAAQ,EAAGC,MAAO,GAiC7DhK,EAAkB,SAAlBA,gBAAmBsE,EAAQW,EAAO9H,EAAW8M,OACxCtY,EAAO,CAAC8Q,QAAS,SACpByH,EAAO/M,EAAU8M,EAAU,MAAQ,MACnCE,EAAehN,EAAU8M,EAAU,KAAO,OAC3C3F,EAAO8F,WAAaH,EACpBtY,EAAKwL,EAAU3O,EAAI,WAAayb,GAAW,IAAM,EACjDtY,EAAKwL,EAAU3O,GAAKyb,EAAU,MAAQ,EACtCtY,EAAK,SAAWuY,EAAOpB,IAAU,EACjCnX,EAAK,SAAWwY,EAAerB,IAAU,EACzCnX,EAAKwL,EAAUnM,GAAKiU,EAAQ,KAC5Bpc,GAAK4d,IAAInC,EAAQ3S,IAElBiQ,GAAY,GACZyI,GAAO,GAuBPC,EAAa,GACbC,EAAc,GAEdjK,EAAY,SAAZA,UAAY7W,UAAS6gB,EAAW7gB,IAAS6gB,EAAW7gB,GAAM+gB,IAAI,SAAAlgB,UAAKA,OAASigB,GAC5ElJ,EAAe,GAgCfoJ,GAAa,EAcbvJ,GAAc,SAAdA,YAAe9T,EAAOsd,OACjBxK,IAAoB9S,GAAUyU,GAIlCK,KACAH,GAAiBpT,GAAcgc,cAAe,EAC9CxgB,GAAWsT,QAAQ,SAAApB,UAAOf,GAAYe,MAAUA,EAAIlR,UAAYkR,EAAI2F,IAAM3F,WACtEuO,EAAetK,EAAU,eAC7B0H,IAASrZ,GAAcsO,OACvByN,GAAcjJ,KACdtX,GAAWsT,QAAQ,SAAApB,GACdf,GAAYe,KACfA,EAAIvP,SAAWuP,EAAIxP,OAAO4S,MAAMoL,eAAiB,QACjDxO,EAAI,MAGNuF,GAAU/H,MAAM,GAAG4D,QAAQ,SAAAlS,UAAKA,EAAEuf,YAClCjJ,GAAc,EACdD,GAAUnE,QAAQ,SAAClS,MACdA,EAAEwf,eAAiBxf,EAAEoX,IAAK,KACzByE,EAAO7b,EAAEoG,KAAKqZ,WAAa,cAAgB,eAC9CC,EAAW1f,EAAEoX,IAAIyE,GAClB7b,EAAEgO,QAAO,EAAM,GACfhO,EAAE2f,iBAAiB3f,EAAEoX,IAAIyE,GAAQ6D,GACjC1f,EAAEuf,aAGJ3C,GAAe,EACf7F,IAAgB,GAChBV,GAAUnE,QAAQ,SAAAlS,OACbqC,EAAMsN,GAAW3P,EAAEob,SAAUpb,EAAE4f,MAClCC,EAA0B,QAAf7f,EAAEoG,KAAKuT,KAAkB3Z,EAAE8f,WAAa9f,EAAE2Z,IAAMtX,EAC3D0d,EAAa/f,EAAEggB,aAAehgB,EAAE0Z,OAASrX,GACzCwd,GAAYE,IAAe/f,EAAEigB,aAAaF,EAAa1d,EAAM,EAAIrC,EAAE0Z,MAAOmG,EAAWpgB,KAAK4C,IAAI0d,EAAa1d,EAAMrC,EAAE0Z,MAAQ,EAAGrX,GAAOrC,EAAE2Z,KAAK,KAE9I5C,IAAgB,GAChB6F,GAAe,EACfyC,EAAanN,QAAQ,SAAA1B,UAAUA,GAAUA,EAAO0P,QAAU1P,EAAO0P,QAAQ,KACzEthB,GAAWsT,QAAQ,SAAApB,GACdf,GAAYe,KACfA,EAAIvP,QAAU6I,sBAAsB,kBAAM0G,EAAIxP,OAAO4S,MAAMoL,eAAiB,WAC5ExO,EAAI2F,KAAO3F,EAAIA,EAAI2F,QAGrBF,GAAmBG,EAAoB,GACvCjB,EAAajJ,QACb0S,KAEArK,EADA2B,GAAiB,GAEjBH,GAAUnE,QAAQ,SAAAlS,UAAK+P,GAAY/P,EAAEoG,KAAK+Z,YAAcngB,EAAEoG,KAAK+Z,UAAUngB,KACzEwW,GAAiBpT,GAAcgc,cAAe,EAC9CrK,EAAU,gBAlDT9W,GAAamF,GAAe,YAAasS,KAoD3C0K,EAAc,EACdC,GAAa,EAEbxL,EAAa,SAAbA,WAAchT,MACC,IAAVA,IAAiB2U,KAAmBF,EAAc,CACrDlT,GAAckd,YAAa,EAC3BvD,IAAYA,GAASnb,OAAO,OACxB6W,EAAIpC,GAAUhV,OACjBiY,EAAOxX,KACPye,EAAkC,IAAjBjH,EAAO0D,EACxBlE,EAASL,GAAKpC,GAAU,GAAGyC,YAC5BuH,GAA2BvH,EAAdsH,GAAwB,EAAI,EACzC5J,KAAmB4J,EAActH,GAC7ByH,IACC5L,KAAoB9F,IAA2C,IAAzByK,EAAO3E,KAChDA,GAAkB,EAClBI,EAAU,cAEXqH,GAASY,EACTA,EAAS1D,GAEN+G,GAAa,EAAG,KACnBjK,GAAKqC,EACS,EAAPrC,MACNC,GAAUD,KAAOC,GAAUD,IAAIxU,OAAO,EAAG2e,GAE1CF,GAAa,WAERjK,GAAK,EAAGA,GAAKqC,EAAGrC,KACpBC,GAAUD,KAAOC,GAAUD,IAAIxU,OAAO,EAAG2e,GAG3Cnd,GAAckd,YAAa,EAE5B1L,EAAS,GAEV8C,EAAmB,CAzSX,OACD,MAwS0B5D,EAASD,EAAQwE,GAAUiF,GAASjF,GAAU8E,GAAQ9E,GAAUgF,GAAMhF,GAAU+E,GAAO,UAAW,aAAc,QAAS,SAAU,kBAAmB,gBAAiB,eAAgB,aAAc,WAAY,cAAe,YAAa,YAAa,SAC3R1E,GAAchB,EAAiB8I,OAAO,CAACvI,GAAQE,GAAS,YAAa,MAAQoF,GAAQ,MAAQC,EAAS,WAAYnF,GAASD,GAAUA,GAAWiF,GAAMjF,GAAW+E,GAAQ/E,GAAWkF,GAASlF,GAAWgF,KA6CxMqD,GAAW,WACXnI,GAAY,SAAZA,UAAYK,MACPA,EAAO,KAITlT,EAAGvG,EAHAgV,EAAQyE,EAAM3Y,EAAEkU,MACnBuE,EAAIE,EAAMtX,OACVL,EAAI,OAEJ2X,EAAM3Y,EAAEwX,OAASla,GAAK+F,KAAKoX,SAAS9B,EAAM3Y,IAAIiW,QAAU,EAClDjV,EAAIyX,EAAGzX,GAAI,EACjB9B,EAAQyZ,EAAM3X,EAAE,GAChByE,EAAIkT,EAAM3X,GACN9B,EACHgV,EAAMzO,GAAKvG,EACDgV,EAAMzO,IAChByO,EAAMgG,eAAezU,EAAEib,QAAQD,GAAU,OAAOE,iBA4BpDrR,GAAc,CAAC0K,KAAK,EAAGC,IAAI,GA+D3BM,GAAa,qCAyFDnX,4BAQZ+C,KAAA,cAAKC,EAAM+J,WACLE,SAAWnE,KAAKwN,MAAQ,OACxBtT,MAAQ8F,KAAK6B,MAAK,GAAM,GACxBkP,QAwBJ2D,EAASC,EAAUC,EAAUC,EAASC,EAAStH,EAAOC,EAAKsH,EAAaC,EAAWC,EAAoBC,EAAkBC,EAAYC,EACrIC,EAAQC,EAAkBC,EAAgBC,EAAUrK,EAAQvX,EAAQ6hB,EAAWC,EAAWC,EAAUC,EAAWC,EAAcxK,EAAayK,EAAmBC,EAC7JC,EAAiB5K,EAAI6K,EAAOC,EAAOC,GAAYC,EAAaC,EAAcC,GAAiBC,GAAYC,GAAkBC,EAAgBC,EArBrI5G,GADL5V,EAAOyK,GAAcpC,GAAUrI,IAAS4J,GAAU5J,IAASA,EAAKyc,SAAY,CAAC1M,QAAS/P,GAAQA,EAAMgY,KAC/FpC,SAAU8G,EAAsO1c,EAAtO0c,YAAajZ,EAAyNzD,EAAzNyD,GAAIkZ,EAAqN3c,EAArN2c,SAAU5C,GAA2M/Z,EAA3M+Z,UAAW6C,EAAgM5c,EAAhM4c,MAAO7M,GAAyL/P,EAAzL+P,QAASiB,GAAgLhR,EAAhLgR,IAAK6L,GAA2K7c,EAA3K6c,WAAYC,GAA+J9c,EAA/J8c,oBAAqB5E,EAA0IlY,EAA1IkY,cAAe6E,EAA2H/c,EAA3H+c,gBAAiBC,EAA0Ghd,EAA1Ggd,eAAgBC,GAA0Fjd,EAA1Fid,KAAM9R,GAAoFnL,EAApFmL,KAAM+R,GAA8Eld,EAA9Ekd,YAAaC,EAAiEnd,EAAjEmd,UAAWvQ,GAAsD5M,EAAtD4M,mBAAoBwQ,GAAkCpd,EAAlCod,cAAeC,GAAmBrd,EAAnBqd,gBACjO7R,GAAYxL,EAAKqZ,YAAerZ,EAAK4M,qBAA0C,IAApB5M,EAAKqZ,WAAwBja,GAAcvE,GACtGyiB,IAAYV,GAAmB,IAAVA,EACrB5H,GAAWrb,EAAWqG,EAAKgV,UAAYhc,IACvCukB,EAAgBrmB,GAAK+F,KAAKoX,SAASW,IACnCjP,GAAarO,GAAYsd,IACzB7H,GAA0H,WAAtG,YAAanN,EAAOA,EAAKwd,QAAUnmB,EAAc2d,GAAU,YAAejP,IAAc,SAC5G0X,GAAY,CAACzd,EAAK0d,QAAS1d,EAAK2d,QAAS3d,EAAK4d,YAAa5d,EAAK6d,aAChE5F,GAAgBqF,IAAYtd,EAAKiY,cAAcpZ,MAAM,KACrDif,GAAU,YAAa9d,EAAOA,EAAK8d,QAAU9F,GAAU8F,QACvDhL,GAAc/M,GAAa,EAAIH,WAAW4E,GAAkBwK,IAAU,SAAWxJ,GAAUlM,GAAK6X,MAAY,EAC5Gtd,GAAOiM,KACPiY,GAAgB/d,EAAK+d,eAAkB,kBAAM/d,EAAK+d,cAAclkB,KAChEmkB,GA1kBa,SAAfC,aAAgBjJ,EAAUjP,SAAatG,IAAAA,EAAGC,IAAAA,GAAI7C,IAAAA,SAAQA,EAAIxF,EAAc2d,EAAU,0BAA4B,kBAAMnY,IAAI4C,IAAK,kBAAOsG,EAAa+C,GAAsBpJ,GAAMsV,EAAS,SAAWtV,KAAQ,GA0kBrLue,CAAajJ,GAAUjP,GAAYyF,IACrD0S,GA1kBgB,SAAlBC,gBAAmB7mB,EAASyO,UAAgBA,IAAevO,GAASC,QAAQH,GAAW2R,GAAe3R,GAAW,kBAAM4R,IA0kBhGiV,CAAgBnJ,GAAUjP,IAC/CqY,GAAW,EACXC,GAAc,EACdC,GAAe,EACfrS,GAAa3R,EAAe0a,GAAUxJ,OAMvC3R,GAAK+f,YAAc/f,GAAK6f,WAAY,EACpC7f,GAAK2f,KAAOhO,GACZ0M,GAAiB,GACjBre,GAAKmb,SAAWA,GAChBnb,GAAK6Y,OAAS9F,GAAqBA,GAAmBsG,KAAKqL,KAAK3R,IAAsBX,GACtF0O,EAAU1O,KACVpS,GAAKmG,KAAOA,EACZ+J,EAAYA,GAAa/J,EAAK+J,UAC1B,oBAAqB/J,IACxBqW,GAAQ,GACkB,OAA1BrW,EAAKwe,kBAA8B7H,GAAW9c,KAE/C0jB,EAAckB,YAAclB,EAAckB,aAAe,CACxD5K,IAAKkB,GAAiBC,GAAUna,IAChC+Y,KAAMmB,GAAiBC,GAAU5V,KAElCvF,GAAK2gB,QAAUA,EAAU+C,EAAckB,YAAYjT,GAAUnM,GAC7DxF,GAAK6kB,cAAgB,SAAA5lB,IACpBojB,EAActS,GAAU9Q,IAAUA,GAKjCmjB,GAAaA,GAAWjR,SAASlS,GAAUmjB,GAAa/kB,GAAK4e,GAAG/L,EAAW,CAAC4U,KAAM,OAAQC,cAAe,MAAOlJ,SAAS,EAAO1K,SAAUkR,EAAa2C,QAAQ,EAAMxJ,WAAY,6BAAM0H,GAAmBA,EAAgBljB,QAH1NoiB,IAAcA,GAAWhS,SAAS,GAAGtC,OACrCsU,GAAa,IAKXlS,IACHA,EAAU/J,KAAK8e,MAAO,EACrB/U,EAAUgV,WAAallB,GAAKmlB,aAAmD,IAAnCjV,EAAU/J,KAAKif,kBAAsD,IAAzBjf,EAAKif,iBAA6BlV,EAAUiB,YAAcjB,EAAU+P,OAAO,GAAG,GAAM,GAC7KjgB,GAAKkQ,UAAYA,EAAU3D,SAC3B2D,EAAUsJ,cAAgBxZ,IACrB6kB,cAAc9B,GACnBb,EAAQ,EACDtY,EAAPA,GAAYsG,EAAU/J,KAAKyD,IAGxB0H,KAEEtB,GAAUsB,MAASA,GAAKrQ,OAC5BqQ,GAAO,CAAC+T,OAAQ/T,wBAEIrN,GAAMgQ,OAAU5W,GAAK4d,IAAI/O,GAAa,CAACjI,GAAOnD,IAAUqa,GAAU,CAACkE,eAAgB,SACxG1gB,GAAWsT,QAAQ,SAAApE,UAAKiC,GAAYjC,IAAMA,EAAExM,UAAY6K,GAAatL,GAAKC,kBAAoBC,GAASqa,MAActN,EAAEvM,QAAS,KAChIuf,EAAW/Q,GAAYwB,GAAK+T,QAAU/T,GAAK+T,OAAyB,WAAhB/T,GAAK+T,OAjkBxC,SAAnBC,iBAAmBpV,UAAa,SAAAjR,UAAS5B,GAAK8C,MAAMmR,KAAKN,GAAoBd,GAAYjR,IAikBRqmB,CAAiBpV,GAA6B,sBAAhBoB,GAAK+T,OAjiB7F,SAAvBE,qBAAuBtU,UAAY,SAAChS,EAAOumB,UAAOpU,GAAiBJ,GAAoBC,GAArCG,CAAgDnS,EAAOumB,EAAG7T,YAiiByC4T,CAAqBrV,IAAkC,IAArBoB,GAAKmU,YAAwB,SAACxmB,EAAOumB,UAAOpU,GAAiBE,GAAK+T,OAAtBjU,CAA8BnS,EAAO4C,KAAa2iB,GAAc,IAAM,EAAIgB,EAAG7T,YAAatU,GAAK8C,MAAMmR,KAAKA,GAAK+T,QAChV/C,EAAehR,GAAKH,UAAY,CAACrP,IAAK,GAAKM,IAAK,GAChDkgB,EAAetS,GAAUsS,GAAgBpG,GAAOoG,EAAaxgB,IAAKwgB,EAAalgB,KAAO8Z,GAAOoG,EAAcA,GAC3GC,GAAkBllB,GAAKyP,YAAYwE,GAAKoU,OAAUrD,EAAc,GAAM,GAAK,eACtExJ,EAASzG,KACZuT,EAAoB9jB,KAAa2iB,GAAc,IAC/CjJ,EAAQoF,EAAQpF,WACZoK,GAAqBnmB,KAAKyD,IAAIjD,GAAKsC,eAAiB,KAAQiZ,GAAU3M,IAAkB2V,KAAa1L,EAoC/F7Y,GAAK4lB,UAAYrB,KAAa1L,GACxC0J,GAAgBjX,SAAQ,OArCyF,KAMhHua,EAAUC,EALP1V,GAAYyI,EAASY,GAAS6H,EACjCyD,EAAgB7U,IAAcuT,GAAWvT,EAAU6U,gBAAkB3U,EACrE2V,EAAWJ,EAAoB,GAAMZ,EAAgB5C,IAAUtgB,KAAasa,IAAU,KAAS,EAC/Fd,EAAUhe,GAAK8C,MAAM+D,OAAOkM,EAAU,EAAIA,EAAU6M,GAAK8I,EAAW,GAAKA,EAAW,MACpFC,EAAa5V,IAA6B,IAAjBkB,GAAK2U,QAAoB,EAAI5K,GAEpD6K,EAAqC5U,GAArC4U,QAAStL,EAA4BtJ,GAA5BsJ,YAAaY,EAAelK,GAAfkK,cACzBqK,EAAWhF,EAASmF,EAAYhmB,IAChC+P,GAAU8V,KAAcA,EAAWG,GACnCF,EAAYtmB,KAAKC,MAAMga,EAAQoM,EAAWvE,GACtCzI,GAAUa,GAAiBD,GAAVZ,GAAmBiN,IAAcjN,EAAQ,IACzD0C,IAAUA,EAAM2J,UAAY3J,EAAMhY,MAAQ0Z,GAAK6I,EAAYjN,WAG1C,IAAjBvH,GAAK2U,UACR5K,EAAUwK,EAAWzV,GAEtBuQ,EAAQmF,EAAW,CAClB3U,SAAUmR,EAAarF,GAAoF,KAA7Ezd,KAAK4C,IAAI6a,GAAK+I,EAAajB,GAAgB9H,GAAK4I,EAAWd,IAA0BgB,EAAW,KAAS,IACvIjB,KAAMxT,GAAKwT,MAAQ,SACnBvhB,KAAM0Z,GAAK6I,EAAYjN,GACvB+B,YAAa,8BAAM2H,GAAgBjX,SAAQ,IAASsP,GAAeA,EAAY5a,KAC/Ewb,iCACCxb,GAAK2B,SACL4iB,GAAWnS,KACPlC,IACHkS,GAAaA,GAAW+D,QAAQ,gBAAiBN,EAAU3V,EAAUkW,OAASlW,EAAUmW,OAASnW,EAAUE,SAASyV,IAErH3D,EAAQC,EAAQjS,IAAcuT,GAAWvT,EAAU6U,gBAAkB/kB,GAAKoQ,SAC1E+S,GAAkBA,EAAenjB,IACjCwb,GAAcA,EAAWxb,MAExB6Y,EAAQwC,EAAUiG,EAAQwE,EAAYjN,EAASwC,EAAUiG,GAC5D4E,GAAWA,EAAQlmB,GAAM2gB,EAAQpF,WAKjChP,SAEJ3C,IAAOiV,GAAKjV,GAAM5J,IAKK2iB,GADvBA,GAHAzM,GAAUlW,GAAKkW,QAAUpW,EAAWoW,KAAoB,IAARiB,IAAgBA,MAGhCjB,GAAQqB,OAASrB,GAAQqB,MAAM+O,WACnB3D,EAAmB3iB,IAE/DmX,IAAc,IAARA,GAAejB,GAAUpW,EAAWqX,IAC1C3I,GAAUqU,KAAiBA,EAAc,CAAC0D,QAASrQ,GAASsQ,UAAW3D,IACnE1L,MACa,IAAf6L,IAAwBA,KAAe5K,KAAa4K,MAAcA,IAAc7L,GAAImB,YAAcnB,GAAImB,WAAWrE,OAAuD,SAA9CtD,GAAkBwG,GAAImB,YAAYrB,UAA6BkB,IAC1LnY,GAAKmX,IAAMA,IACXyJ,EAAWvjB,GAAK+F,KAAKoX,SAASrD,KAChBC,OAYbmK,EAAmBX,EAASa,UAXxB6B,KACHA,EAAYxjB,EAAWwjB,MACTA,EAAUV,WAAaU,EAAYA,EAAUvI,SAAWuI,EAAUmD,eAChF7F,EAAS8F,iBAAmBpD,EAC5BA,IAAc1C,EAAStJ,YAAciB,GAAU+K,KAEhD1C,EAASxJ,OAASA,EAASkM,GAAa1iB,GAAKyS,cAAc,OAC3D+D,EAAOuP,UAAUnW,IAAI,cACrB5G,GAAMwN,EAAOuP,UAAUnW,IAAI,cAAgB5G,GAC3CgX,EAASa,SAAWF,EAAmBhJ,GAAUpB,MAIjC,IAAjBhR,EAAKygB,SAAqBvpB,GAAK4d,IAAI9D,GAAK,CAACyP,SAAS,IAClD5mB,GAAKoX,OAASA,EAASwJ,EAASxJ,OAChCC,EAAK1G,GAAkBwG,IACvB2K,EAAezK,EAAG2L,GAAarR,GAAUhM,KACzC+b,EAAYrkB,GAAKkE,YAAY4V,IAC7BwK,EAAYtkB,GAAKwpB,YAAY1P,GAAKxF,GAAU3O,EAAGiV,IAE/Cf,GAAWC,GAAKC,EAAQC,GACxBoK,EAAWlJ,GAAUpB,KAElB8M,GAAS,CACZ7C,EAAapR,GAAUiU,IAAWrT,GAAaqT,GAAS/F,IAAmBA,GAC3EgD,EAAqBtO,GAAc,iBAAkBhJ,EAAIuR,GAAUxJ,GAAWyP,EAAY,GAC1FD,EAAmBvO,GAAc,eAAgBhJ,EAAIuR,GAAUxJ,GAAWyP,EAAY,EAAGF,GACzFrhB,EAASqhB,EAAmB,SAAWvP,GAAU3L,GAAGH,QAChDihB,EAAUhnB,EAAWtC,EAAc2d,GAAU,YAAcA,IAC/D6F,EAAc/U,KAAK+U,YAAcpO,GAAc,QAAShJ,EAAIkd,EAASnV,GAAWyP,EAAYvhB,EAAQ,EAAGkT,IACvGkO,EAAYhV,KAAKgV,UAAYrO,GAAc,MAAOhJ,EAAIkd,EAASnV,GAAWyP,EAAYvhB,EAAQ,EAAGkT,IACjGA,KAAuB2P,EAAiBrlB,GAAKwpB,YAAY,CAAC7F,EAAaC,GAAYtP,GAAU3O,EAAGiV,KAC1F3E,IAAsB3V,GAASyD,SAAsD,IAA5C5D,EAAc2d,GAAU,kBA1rBrD,SAApB4L,kBAAoBtpB,OACfma,EAAWjH,GAAkBlT,GAASma,SAC1Cna,EAAQwW,MAAM2D,SAAyB,aAAbA,GAAwC,UAAbA,EAAwBA,EAAW,WAyrBtFmP,CAAkB7a,GAAajI,GAAQkX,IACvC9d,GAAK4d,IAAI,CAACiG,EAAoBC,GAAmB,CAACyF,SAAS,IAC3D7E,EAAoB1kB,GAAKwpB,YAAY3F,EAAoBvP,GAAU3O,EAAGiV,IACtEgK,EAAkB5kB,GAAKwpB,YAAY1F,EAAkBxP,GAAU3O,EAAGiV,QAIhElF,GAAoB,KACnBiU,EAAcjU,GAAmB5M,KAAK4V,SACzCkL,EAAYlU,GAAmB5M,KAAK+gB,eACrCnU,GAAmBoU,cAAc,WAAY,WAC5CnnB,GAAK2B,OAAO,EAAG,EAAG,GAClBqlB,GAAeA,EAAYI,MAAMrU,GAAoBkU,GAAa,SAIpEjnB,GAAKqnB,SAAW,kBAAMjR,GAAUA,GAAUxY,QAAQoC,IAAQ,IAC1DA,GAAKsnB,KAAO,kBAAMlR,GAAUA,GAAUxY,QAAQoC,IAAQ,IAEtDA,GAAK+N,OAAS,SAACA,EAAQwZ,OACjBA,SAAevnB,GAAK8N,MAAK,OAC1B0Z,GAAe,IAAXzZ,IAAqB/N,GAAKsQ,QACjCmX,EAAiBtS,GACdqS,IAAMxnB,GAAKmlB,aACVqC,IACHhF,GAAahjB,KAAK4C,IAAIgQ,KAAcpS,GAAK6Y,OAAOrC,KAAO,GACvDiO,GAAezkB,GAAKoQ,SACpBqS,GAAmBvS,GAAaA,EAAUE,YAE3C4Q,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkBlP,QAAQ,SAAAiI,UAAKA,EAAEjG,MAAMgD,QAAUuQ,EAAI,OAAS,UACtHA,IACHrS,GAAcnV,IACT2B,OAAO6lB,IAETrQ,IAASkM,IAAgBrjB,GAAK4lB,WAC7B4B,EAncM,SAAdE,YAAevQ,EAAKC,EAAQsB,GAC3BL,GAAUK,OACN9Z,EAAQuY,EAAII,SACZ3Y,EAAM8nB,eACTrO,GAAUzZ,EAAM0Y,kBACV,GAAIH,EAAII,MAAMC,UAAW,KAC3BhE,EAAS4D,EAAOkB,WAChB9E,IACHA,EAAOa,aAAa8C,EAAKC,GACzB5D,EAAOqD,YAAYO,IAGrBD,EAAII,MAAMC,WAAY,EAwblBkQ,CAAYvQ,GAAKC,EAAQmK,GAEzBrK,GAAWC,GAAKC,EAAQzG,GAAkBwG,IAAMG,IAGlDkQ,GAAKxnB,GAAK2B,OAAO6lB,GACjBrS,GAAcsS,EACdznB,GAAKmlB,WAAaqC,IAIpBxnB,GAAKsf,QAAU,SAACqI,EAAM/lB,EAAOgW,EAAUgQ,OACjCzS,IAAgBnV,GAAKsQ,SAAa1O,KAGnCuV,IAAOwQ,GAAQjT,GAClB1W,GAAamF,cAAe,YAAasS,UAGzCc,IAAkB2N,IAAiBA,GAAclkB,IAClDmV,GAAcnV,GACV2gB,EAAQpF,QAAU3D,IACrB+I,EAAQpF,MAAMzN,OACd6S,EAAQpF,MAAQ,GAEjB6G,IAAcA,GAAW7V,QACzB0W,IAAuB/S,GAAaA,EAAUnC,OAAO,CAACD,MAAM,IAAQ+Z,aACpE7nB,GAAKmlB,YAAcnlB,GAAK+N,QAAO,GAAM,GACrC/N,GAAKuf,eAAgB,MAapBlI,EAAIsC,EAAQd,EAAQiP,EAAYC,EAAUC,EAAYC,EAAQC,EAAgBC,EAASC,EAAcC,EAAgBC,EAAmBC,EAZrIhW,EAAO4R,KACVnL,EAAiBqL,KACjBjiB,EAAM2Q,GAAqBA,GAAmB5B,WAAazB,GAAWyL,GAAUxJ,IAChF6W,EAAiBlH,GAAU,IAC3BzhB,EAAS,EACT4oB,EAAiBb,GAAa,EAC9Bc,EAAY1Y,GAAU4H,GAAYA,EAAS8B,IAAMvT,EAAKuT,IACtDiP,EAAmBxiB,EAAKyiB,YAAc1S,GACtC2S,EAAc7Y,GAAU4H,GAAYA,EAAS6B,MAAStT,EAAKsT,QAAyB,IAAftT,EAAKsT,OAAgBvD,GAAeiB,GAAM,MAAQ,SAAnB,GACpG2R,EAAkB9oB,GAAK8oB,gBAAkB3iB,EAAK2iB,iBAAmBhpB,EAAWqG,EAAK2iB,gBAAiB9oB,IAClG+oB,EAAgB7S,IAAW1W,KAAK4C,IAAI,EAAGgU,GAAUxY,QAAQoC,MAAW,EACpEe,EAAIgoB,MAED9E,IAAWjU,GAAU4H,KACxB0Q,EAAoBjrB,GAAKkE,YAAY2f,EAAoBvP,GAAUnM,GACnE+iB,EAAkBlrB,GAAKkE,YAAY4f,EAAkBxP,GAAUnM,IAEzDzE,MACNinB,EAAa5R,GAAUrV,IACZ2Y,KAAOsO,EAAW1I,QAAQ,EAAG,KAAOnK,GAAcnV,MAC7DioB,EAASD,EAAW7Q,MACL8Q,IAAW/R,IAAW+R,IAAW9Q,IAAO8Q,IAAWa,GAAqBd,EAAW7C,cAChFiD,EAAjBA,GAAgC,IACnBY,QAAQhB,GACrBA,EAAWja,QAAO,GAAM,IAErBia,IAAe5R,GAAUrV,KAC5BgoB,IACAhoB,SAGF+O,GAAY+Y,KAAiBA,EAAcA,EAAY7oB,KACvD6oB,EAActa,GAAYsa,EAAa,QAAS7oB,IAChDyZ,EAAQd,GAAekQ,EAAa3S,GAAS3D,EAAMZ,GAAWS,KAAc4O,EAAaE,EAAoBlhB,GAAMgZ,EAAgBC,GAAa3F,GAAkBlR,EAAK2Q,GAAoB/S,GAAK+f,aAAe,iBAAmB5I,IAAO,KAAQ,GACjPrH,GAAY4Y,KAAeA,EAAYA,EAAU1oB,KAC7CwO,GAAUka,KAAeA,EAAU9qB,QAAQ,SACzC8qB,EAAU9qB,QAAQ,KACtB8qB,GAAala,GAAUqa,GAAeA,EAAY7jB,MAAM,KAAK,GAAK,IAAM0jB,GAExE7oB,EAASyS,GAAYoW,EAAUja,OAAO,GAAI8D,GAC1CmW,EAAYla,GAAUqa,GAAeA,GAAe9V,GAAqB1V,GAAK8C,MAAMoZ,SAAS,EAAGxG,GAAmB5B,WAAY4B,GAAmByG,cAAcC,MAAO1G,GAAmByG,cAAcE,IAAKD,GAASA,GAAS5Z,EAC/N8oB,EAAmBzS,KAGrBwS,EAAYna,GAAYma,EAAW,MAAO1oB,IAC1C0Z,EAAMla,KAAK4C,IAAIqX,EAAOd,GAAe+P,IAAcC,EAAmB,SAAWvmB,GAAMumB,EAAkBpW,EAAMZ,GAAWS,KAAevS,EAAQohB,EAAWE,EAAkBnhB,GAAMgZ,EAAgBC,GAAa3F,GAAkBlR,EAAK2Q,GAAoB/S,GAAK6f,WAAa,gBAAkB,KAEhShgB,EAAS,EACTkB,EAAIgoB,EACGhoB,MAENknB,GADAD,EAAa5R,GAAUrV,IACHoW,MACN6Q,EAAWvO,MAAQuO,EAAWiB,UAAYxP,IAAU1G,IAAuC,EAAjBiV,EAAWtO,MAClGrC,EAAK2Q,EAAWtO,KAAO1Z,GAAK+f,YAAcvgB,KAAK4C,IAAI,EAAG4lB,EAAWvO,OAASuO,EAAWvO,QAC/EwO,IAAW/R,IAAW8R,EAAWvO,MAAQuO,EAAWiB,SAAWxP,GAAUwO,IAAWa,IAAoBpc,MAAMmc,KACnHhpB,GAAUwX,GAAM,EAAI2Q,EAAW5X,WAEhC6X,IAAW9Q,KAAQsR,GAAkBpR,OAGvCoC,GAAS5Z,EACT6Z,GAAO7Z,EACPG,GAAK+f,cAAgB/f,GAAK+f,aAAelgB,GAErCG,GAAK6f,YAActJ,KACtBvW,GAAK6f,UAAYnG,IAAQ,KACzBA,EAAMla,KAAKsC,IAAI4X,EAAKhK,GAAWyL,GAAUxJ,MAE1C2P,EAAU5H,EAAMD,IAAYA,GAAS,MAAS,KAE1C+O,IACH/D,GAAepnB,GAAK8C,MAAM+D,MAAM,EAAG,EAAG7G,GAAK8C,MAAM+oB,UAAUzP,EAAOC,EAAK8I,MAExExiB,GAAKipB,SAAWR,EACZzH,GAAenhB,KAClBwX,EAAK,IACF1F,GAAU3O,GAAK,KAAOnD,EACzBipB,IAAoBzR,EAAG1F,GAAUnM,GAAK,KAAO4M,MAC7C/U,GAAK4d,IAAI,CAAC+F,EAAaC,GAAY5J,KAGhCF,IAASwF,IAAgB3c,GAAK0Z,KAAOhK,GAAWyL,GAAUxJ,KAuEvD,GAAIuE,IAAW9D,OAAiBW,OACtC4G,EAASzD,GAAQoC,WACVqB,GAAUA,IAAW1V,IACvB0V,EAAOwP,aACV1P,GAASE,EAAOwP,WAChBzP,GAAOC,EAAOwP,YAEfxP,EAASA,EAAOrB,gBA7EjBjB,EAAK1G,GAAkBwG,IACvB2Q,EAAanW,KAAc3Q,GAC3B6X,EAASzG,KACTwP,EAAW7V,WAAW2V,EAAU/P,GAAU3O,IAAMylB,GAC3CrmB,GAAa,EAANsX,IAEX2O,EAAiB,CAACpU,MADlBoU,GAAkBnc,GAActL,GAAKC,kBAAoBC,GAAUqa,IAAUlH,MACpChV,MAAOopB,EAAe,WAAa1W,GAAU3O,EAAEomB,gBACpFld,IAAmF,WAArEyE,GAAkB1M,IAAO,WAAa0N,GAAU3O,EAAEomB,iBACnEf,EAAepU,MAAM,WAAatC,GAAU3O,EAAEomB,eAAiB,WAGjElS,GAAWC,GAAKC,EAAQC,GACxBoK,EAAWlJ,GAAUpB,IAErBwC,EAASlK,GAAW0H,IAAK,GACzB+Q,EAAiB5U,IAAoB7S,EAAe0a,GAAU2M,EAAaviB,GAAcvE,GAApDP,GACjCuiB,KACH1L,EAAc,CAAC0L,GAAarR,GAAUhM,IAAK2b,EAASmH,EAAiBxQ,KACzDlY,EAAIqX,GAChBrW,EAAKiiB,KAAe7K,GAAYpH,GAASoG,GAAKxF,IAAa2P,EAASmH,EAAiB,KAEpFnR,EAAYrW,KAAK0Q,GAAU/L,EAAG7E,EAAIkX,IACP,SAA3Bb,EAAOnD,MAAM4D,YAAyBT,EAAOnD,MAAM4D,UAAY9W,EAAIkX,KAEpEI,GAAUf,GACNwR,GACH1S,GAAUnE,QAAQ,SAAAlS,GACbA,EAAEoX,MAAQ2R,IAAyC,IAAtB/oB,EAAEoG,KAAK6c,aACvCjjB,EAAEwf,eAAgB,KAIrBjM,IAAoBlB,GAAWoQ,MAE/BzhB,EAAIgQ,GAASoG,GAAKxF,MACc,SAA3ByF,EAAOnD,MAAM4D,YAAyBT,EAAOnD,MAAM4D,UAAY9W,EAAIkX,IAErE3E,MACHyU,EAAW,CACV/N,IAAML,EAAOK,KAAO8N,EAAajP,EAASY,EAAQyO,GAAmBjQ,GACrE8B,KAAOJ,EAAOI,MAAQ+N,EAAaI,EAAiBrP,EAASY,GAAUxB,GACvEF,UAAW,aACXH,SAAU,UAEFI,IAAU+P,EAAQ,SAAmBvoB,KAAK6pB,KAAK1P,EAAOrK,OAAS2I,GACxE8P,EAAS7P,IAAW6P,EAAQ,UAAoBvoB,KAAK6pB,KAAK1P,EAAOnK,QAAUyI,GAC3E8P,EAAS3P,IAAW2P,EAAS3P,GAAUgF,IAAQ2K,EAAS3P,GAAU8E,IAAU6K,EAAS3P,GAAUiF,IAAW0K,EAAS3P,GAAU+E,IAAS,IACtI4K,EAAS5P,IAAYd,EAAGc,IACxB4P,EAAS5P,GAAWiF,IAAQ/F,EAAGc,GAAWiF,IAC1C2K,EAAS5P,GAAW+E,IAAU7F,EAAGc,GAAW+E,IAC5C6K,EAAS5P,GAAWkF,IAAWhG,EAAGc,GAAWkF,IAC7C0K,EAAS5P,GAAWgF,IAAS9F,EAAGc,GAAWgF,IAC3CqE,EA7hBS,SAAb8H,WAAc5Q,EAAOqP,EAAUwB,WAI7B/jB,EAHG+K,EAAS,GACZiI,EAAIE,EAAMtX,OACVL,EAAIwoB,EAAc,EAAI,EAEhBxoB,EAAIyX,EAAGzX,GAAK,EAClByE,EAAIkT,EAAM3X,GACVwP,EAAOtP,KAAKuE,EAAIA,KAAKuiB,EAAYA,EAASviB,GAAKkT,EAAM3X,EAAE,WAExDwP,EAAOxQ,EAAI2Y,EAAM3Y,EACVwQ,EAmhBa+Y,CAAW/H,EAAkBwG,EAAU1E,IACxD9M,IAAkBnE,GAAW,IAE1BlC,GACHiY,EAAUjY,EAAUgV,SACpBzI,GAAoB,GACpBvM,EAAU+P,OAAO/P,EAAUiB,YAAY,GAAM,GAC7C0Q,EAAYH,EAAU/P,GAAU3O,GAAK4e,EAAWN,EAASmH,EACzDzG,EAA0C,EAA/BxiB,KAAKyD,IAAIqe,EAASO,GAC7BvO,IAAoB0O,GAAYR,EAAexT,OAAOwT,EAAepgB,OAAS,EAAG,GACjF8O,EAAU+P,OAAO,GAAG,GAAM,GAC1BkI,GAAWjY,EAAU2X,YAAW,GAChC3X,EAAUsD,QAAUtD,EAAUO,UAAUP,EAAUO,aAClDgM,GAAoB,IAEpBoF,EAAYP,EAEb+G,IAAmBA,EAAeppB,MAASopB,EAAepU,MAAM,WAAatC,GAAU3O,EAAEomB,eAAiBf,EAAeppB,MAASopB,EAAepU,MAAMgG,eAAe,YAActI,GAAU3O,IAW/LolB,GAAgBA,EAAanW,QAAQ,SAAAlS,UAAKA,EAAEgO,QAAO,GAAO,KAC1D/N,GAAKyZ,MAAQA,EACbzZ,GAAK0Z,IAAMA,EACXoH,EAAUC,EAAUxK,GAAiBiM,GAAapQ,KAC7CW,IAAuBwD,KAC3BuK,EAAU0B,IAAcpQ,GAAWoQ,IACnCxiB,GAAK6Y,OAAOrC,IAAM,GAEnBxW,GAAK+N,QAAO,GAAO,GACnByW,GAAc3iB,KACV0gB,KACHgC,IAAY,EAEZhC,GAAgBjX,SAAQ,IAEzB6J,GAAc,EACdjF,GAAauT,KAAavT,EAAUgV,UAAYzC,KAAqBvS,EAAUE,aAAeqS,IAAoBvS,EAAUE,SAASqS,IAAoB,GAAG,GAAMxC,OAAO/P,EAAUmJ,QAAQ,GAAM,IAC7LmP,GAAkB/D,KAAiBzkB,GAAKoQ,UAAY2C,IAAsBkQ,MAC7E/S,IAAcuT,IAAYvT,EAAU6U,cAAchS,IAAsB0G,GAAS,OAAUgL,GAAepnB,GAAK8C,MAAM+oB,UAAUzP,EAAOC,EAAK,GAAK+K,IAAc,GAC9JzkB,GAAKoQ,SAAWoY,IAAoB1H,EAAUrH,GAAS6H,IAAWmD,GAAgB,EAAIA,IAEvFtN,IAAO6L,KAAe5L,EAAO+R,WAAa3pB,KAAKC,MAAMO,GAAKoQ,SAAWyR,IACrEO,IAAcA,GAAWyF,aAEpBnb,MAAM4b,KACVA,GAAqBjrB,GAAKkE,YAAY2f,EAAoBvP,GAAUnM,GACpE+iB,GAAmBlrB,GAAKkE,YAAY4f,EAAkBxP,GAAUnM,GAChEwV,GAAakG,EAAoBvP,GAAW2W,GAC5CtN,GAAagG,EAAarP,GAAW2W,GAAqBV,GAAa,IACvE5M,GAAamG,EAAkBxP,GAAW4W,GAC1CvN,GAAaiG,EAAWtP,GAAW4W,GAAmBX,GAAa,KAGpEY,IAAmBjS,IAAkBvW,GAAK2B,UAEtCue,IAAc3J,IAAmB8K,IACpCA,GAAqB,EACrBnB,GAAUlgB,IACVqhB,GAAqB,KAIvBrhB,GAAKsC,YAAc,kBAAQ8P,KAAe2O,IAAYlf,KAAasa,IAAU,KAAS,GAEtFnc,GAAKwpB,aAAe,WACnBvZ,GAAcjQ,GAAK0Q,mBACfR,IACHkS,GAAaA,GAAWhS,SAAS,GAAOF,EAAU8U,SAA4DvB,IAAYxT,GAAcC,EAAWlQ,GAAK2R,UAAY,EAAG,GAA1G1B,GAAcC,EAAWA,EAAUC,cAIlGnQ,GAAKypB,cAAgB,SAAAC,UAASxZ,GAAaA,EAAUgB,SAAYuI,GAASzZ,GAAKsf,WAAa7F,GAAUvJ,EAAUgB,OAAOwY,GAASxZ,EAAUiB,WAAcmQ,GAAW,GAEnKthB,GAAK2pB,YAAc,SAAAhmB,OACd5C,EAAIqV,GAAUxY,QAAQoC,IACzBgD,EAAqB,EAAjBhD,GAAK2R,UAAgByE,GAAU/H,MAAM,EAAGtN,GAAG6oB,UAAYxT,GAAU/H,MAAMtN,EAAE,UACtEyN,GAAU7K,GAAQX,EAAE4K,OAAO,SAAA7N,UAAKA,EAAEoG,KAAKqd,kBAAoB7f,IAAQX,GAAG4K,OAAO,SAAA7N,UAAsB,EAAjBC,GAAK2R,UAAgB5R,EAAE2Z,KAAOD,EAAQ1Z,EAAE0Z,OAASC,KAI5I1Z,GAAK2B,OAAS,SAACU,EAAOie,EAAgBuJ,OACjC9W,IAAuB8W,GAAcxnB,OAOxCujB,EAAqBkE,EAAaC,EAAQC,EAAcC,EAASC,EAASC,EAJvEtR,GAA4B,IAAnBtC,GAA0BiM,GAAaxiB,GAAK6Y,SACxDrT,EAAInD,EAAQ,GAAKwW,EAASY,GAAS6H,EACnC8I,EAAU5kB,EAAI,EAAI,EAAQ,EAAJA,EAAQ,EAAIA,GAAK,EACvCif,EAAezkB,GAAKoQ,YAEjBkQ,IACHS,EAAUD,EACVA,EAAU/N,GAAqBX,KAAeyG,EAC1CvH,KACH6Q,EAAQD,EACRA,EAAQhS,IAAcuT,GAAWvT,EAAU6U,gBAAkBqF,IAI3D/L,GAAiBlH,KAAQhC,KAAgBjW,IAAYwV,MACnD0V,GAAW3Q,EAAQZ,GAAWA,EAASkI,IAAYlf,KAAasa,IAAWkC,EAC/E+L,EAAU,KACY,IAAZA,GAAiB1Q,EAAMb,GAAWA,EAASkI,IAAYlf,KAAasa,IAAWkC,IACzF+L,EAAU,QAGRA,IAAY3F,GAAgBzkB,GAAKsQ,QAAS,IAI7C0Z,GADAC,GAFArE,EAAW5lB,GAAK4lB,WAAawE,GAAWA,EAAU,OACpC3F,GAAgBA,EAAe,OAEjB2F,KAAc3F,EAC1CzkB,GAAK2R,UAAsB8S,EAAV2F,EAAyB,GAAK,EAC/CpqB,GAAKoQ,SAAWga,EAEZJ,IAAiB7U,KACpB2U,EAAcM,IAAY3F,EAAe,EAAgB,IAAZ2F,EAAgB,EAAqB,IAAjB3F,EAAqB,EAAI,EACtFhB,KACHsG,GAAWE,GAA8C,SAAnC7L,GAAc0L,EAAc,IAAiB1L,GAAc0L,EAAc,IAAO1L,GAAc0L,GACpHK,EAAiBja,IAAyB,aAAX6Z,GAAoC,UAAXA,GAAsBA,KAAU7Z,KAI1FsT,KAAoByG,GAAWE,KAAoBA,GAAkBpH,IAAU7S,KAAeJ,GAAY0T,IAAmBA,GAAgBxjB,IAAQA,GAAK2pB,YAAYnG,IAAiBvR,QAAQ,SAAAlS,UAAKA,EAAEypB,kBAEjM/F,MACArB,IAAejN,IAAgBjW,GAQxBgR,GACVA,EAAU6U,cAAcqF,KAAYjV,KAAgBqP,KAAeniB,KARlE+f,GAAWiI,IAAIC,MAAQlI,GAAWmI,SAAWnI,GAAWkI,OAAUlI,GAAWnC,OAAOmC,GAAWiI,IAAIC,MAAQlI,GAAWmI,QACnHnI,GAAW+D,QACd/D,GAAW+D,QAAQ,gBAAiBiE,EAASla,EAAUkW,OAASlW,EAAUmW,QAE1EjE,GAAWjc,KAAK4e,cAAgBqF,EAChChI,GAAWyF,aAAavc,aAMvB6L,MACH9U,GAAS2gB,KAAe5L,EAAOnD,MAAM+O,GAAarR,GAAUhM,KAAOmc,GAC9DxO,IAEE,GAAI0W,EAAc,IACxBE,GAAW7nB,GAAmBoiB,EAAV2F,GAAoCvR,EAAVa,EAAM,GAAcb,EAAS,GAAKnJ,GAAWyL,GAAUxJ,IACjG0R,MACEhhB,IAAUujB,IAAYsE,EAK1B9P,GAAUjD,GAAKC,OALqB,KAChCuC,EAASlK,GAAW0H,IAAK,GAC5BtX,EAASgZ,EAASY,EACnBW,GAAUjD,GAAKlT,GAAQ0V,EAAOK,KAAOrI,KAAc3Q,GAAYnB,EAAS,GAAMoY,GAAM0B,EAAOI,MAAQpI,KAAc3Q,GAAY,EAAInB,GAAWoY,IAK9II,GAAUuN,GAAYsE,EAAU1I,EAAiBC,GAChDO,GAAYoI,EAAU,GAAKxE,GAAajE,EAAUC,GAAwB,IAAZwI,GAAkBF,EAAsB,EAAZrI,UAb3FF,EAAU5S,GAAO6S,EAAWC,EAAYuI,KAgB1C9Y,IAASqP,EAAQpF,OAAUpG,IAAgBjW,IAAYqjB,GAAgBjX,SAAQ,GAC/EuX,IAAgBoH,GAAY7G,IAAQgH,IAAYA,EAAU,IAAMxN,MAAsB5F,GAAS6L,EAAY0D,SAAStU,QAAQ,SAAAnU,UAAMA,EAAG6oB,UAAUf,GAAYxC,GAAO,MAAQ,UAAUP,EAAY2D,cAChMzK,GAAa0H,IAAaphB,GAAS0Z,EAAS/b,IACxCgqB,IAAiB7U,IAChBsO,KACC0G,IACY,aAAXJ,EACH7Z,EAAU3D,QAAQwY,cAAc,GACX,UAAXgF,EACV7Z,EAAU5E,SAAQ,GAAMiB,QACH,YAAXwd,EACV7Z,EAAU5E,SAAQ,GAElB4E,EAAU6Z,MAGZhO,GAAYA,EAAS/b,MAElBiqB,GAAYrN,KACfkG,GAAYmH,GAAW5Z,GAAUrQ,GAAM8iB,GACvCc,GAAUkG,IAAgBzZ,GAAUrQ,GAAM4jB,GAAUkG,IACpD1G,KAAqB,IAAZgH,EAAgBpqB,GAAK8N,MAAK,EAAO,GAAM8V,GAAUkG,GAAe,GACpEG,GAEJrG,GADAkG,EAA0B,IAAZM,EAAgB,EAAI,IACR/Z,GAAUrQ,GAAM4jB,GAAUkG,KAGlDvG,KAAkBqC,GAAYpmB,KAAKyD,IAAIjD,GAAKsC,gBAAkByN,GAAUwT,IAAiBA,GAAgB,QAC5GtT,GAAcjQ,GAAK0Q,mBACnB0R,GAAaA,GAAWhS,SAAS,GAAKH,GAAcC,EAAsB,YAAX6Z,EAAuB,GAAKK,EAAS,KAE3F3G,IAAY1H,IAAa5G,IACnC4G,EAAS/b,OAIPiiB,EAAiB,KAChBuI,EAAIzX,GAAqB8F,EAAS9F,GAAmB5B,YAAc4B,GAAmBoH,eAAiB,GAAKtB,EAChHkJ,EAAkByI,GAAKtJ,EAAmBtC,WAAa,EAAI,IAC3DqD,EAAgBuI,GAEjB9H,GAAkBA,GAAgB7J,EAAS9F,GAAmB5B,YAAc4B,GAAmBoH,eAAiB,MAGjHna,GAAKyN,OAAS,SAACpL,EAAOid,GAChBtf,GAAKsQ,UACTtQ,GAAKsQ,SAAU,EACftS,GAAamd,GAAU,SAAUjG,IACjChJ,IAAclO,GAAamd,GAAU,SAAU3c,IAC/C0lB,IAAiBlmB,GAAamF,cAAe,cAAe+gB,KAC9C,IAAV7hB,IACHrC,GAAKoQ,SAAWqU,GAAe,EAC/B3D,EAAUC,EAAUwD,GAAWnS,OAEpB,IAAZkN,GAAqBtf,GAAKsf,YAI5Btf,GAAKob,SAAW,SAAA9J,UAAQA,GAAQqP,EAAUA,EAAQpF,MAAQ6G,IAE1DpiB,GAAKggB,aAAe,SAACyK,EAAUC,EAAQC,EAAW/C,MAC7C7U,GAAoB,KACnByS,EAAKzS,GAAmByG,cAC3BrI,EAAW4B,GAAmB5B,WAC9BmQ,EAASkE,EAAG9L,IAAM8L,EAAG/L,MACtBgR,EAAWjF,EAAG/L,MAAQ6H,EAASmJ,EAAWtZ,EAC1CuZ,EAASlF,EAAG/L,MAAQ6H,EAASoJ,EAASvZ,EAEvCnR,GAAKsf,SAAQ,GAAO,EAAO,CAAC7F,MAAO/K,GAAW+b,EAAUE,KAAe3qB,GAAK+f,aAAcrG,IAAKhL,GAAWgc,EAAQC,KAAe3qB,GAAK6f,YAAa+H,GACnJ5nB,GAAK2B,UAGN3B,GAAK0f,iBAAmB,SAAAkL,MACnBtT,GAAesT,EAAQ,KACtB7pB,EAAIuW,EAAY1Z,QAAQ+T,GAAU/L,GAAK,EAC3C0R,EAAYvW,GAAMgL,WAAWuL,EAAYvW,IAAM6pB,EAAU3S,GACzDX,EAAY,GAAMvL,WAAWuL,EAAY,IAAMsT,EAAU3S,GACzDI,GAAUf,KAIZtX,GAAK2N,QAAU,SAACtL,EAAOwoB,MAClB7qB,GAAKsQ,WACE,IAAVjO,GAAmBrC,GAAK+N,QAAO,GAAM,GACrC/N,GAAKsQ,QAAUtQ,GAAK4lB,UAAW,EAC/BiF,GAAmBzI,IAAcA,GAAW7V,QAC5CiW,GAAa,EACb5B,IAAaA,EAAS5K,QAAU,GAChCkO,IAAiB5lB,GAAgB6E,cAAe,cAAe+gB,IAC3D3B,KACHA,GAAgBhW,QAChBoU,EAAQpF,OAASoF,EAAQpF,MAAMzN,SAAW6S,EAAQpF,MAAQ,KAEtDrP,IAAY,SACZnL,EAAIqV,GAAUhV,OACXL,QACFqV,GAAUrV,GAAGoa,WAAaA,IAAY/E,GAAUrV,KAAOf,UAI5D1B,GAAgB6c,GAAU,SAAUjG,IACpChJ,IAAc5N,GAAgB6c,GAAU,SAAU3c,MAKrDwB,GAAK8N,KAAO,SAACC,EAAQ8c,GACpB7qB,GAAK2N,QAAQI,EAAQ8c,GACrBzI,KAAeyI,GAAkBzI,GAAWtU,OAC5ClE,UAAciV,GAAKjV,OACf7I,EAAIqV,GAAUxY,QAAQoC,IACrB,GAALe,GAAUqV,GAAUpI,OAAOjN,EAAG,GAC9BA,IAAMoV,IAAmB,EAAbiK,IAAkBjK,KAG9BpV,EAAI,EACJqV,GAAUnE,QAAQ,SAAAlS,UAAKA,EAAEob,WAAanb,GAAKmb,WAAapa,EAAI,KAC5DA,GAAKwV,KAAmBvW,GAAK6Y,OAAOrC,IAAM,GAEtCtG,IACHA,EAAUsJ,cAAgB,KAC1BzL,GAAUmC,EAAUnC,OAAO,CAACD,MAAM,IAClC+c,GAAkB3a,EAAUpC,QAE7BkT,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkBlP,QAAQ,SAAAiI,UAAKA,EAAE5B,YAAc4B,EAAE5B,WAAWzB,YAAYqD,KACpI4C,KAAa9c,KAAS8c,GAAW,GAC7B3F,KACHyJ,IAAaA,EAAS5K,QAAU,GAChCjV,EAAI,EACJqV,GAAUnE,QAAQ,SAAAlS,UAAKA,EAAEoX,MAAQA,IAAOpW,MACxCA,IAAM6f,EAASxJ,OAAS,IAEzBjR,EAAK2kB,QAAU3kB,EAAK2kB,OAAO9qB,KAG5BoW,GAAUnV,KAAKjB,IACfA,GAAKyN,QAAO,GAAO,GACnBkV,GAAsBA,EAAmB3iB,IAErCkQ,GAAaA,EAAUM,MAAQ8Q,EAAQ,KACtCyJ,EAAa/qB,GAAK2B,OACtB3B,GAAK2B,OAAS,WACb3B,GAAK2B,OAASopB,EACdtR,GAASC,GAAO1Z,GAAKsf,WAEtBjiB,GAAKyP,YAAY,IAAM9M,GAAK2B,QAC5B2f,EAAS,IACT7H,EAAQC,EAAM,OAEd1Z,GAAKsf,UAENnI,IAzgCkB,SAAnB6T,sBACKnO,KAAoBoC,GAAY,KAC/BrV,EAAKiT,GAAkBoC,GAC3B9U,sBAAsB,kBAAMP,IAAOqV,IAAcvJ,IAAY,MAsgCvDsV,aAvqBDrpB,OAASsK,KAAKqT,QAAUrT,KAAK6B,KAAOgB,kBA2qBpCX,SAAP,kBAAgB/K,UACVS,IACJxG,GAAO+F,GAAQhG,KACf4R,MAAmB1R,OAAOwG,UAAYX,cAAcsK,SACpD5J,EAAemZ,IAETnZ,iBAGDiN,SAAP,kBAAgBzQ,MACXA,MACE,IAAImF,KAAKnF,EACb8d,GAAU3Y,GAAKnF,EAAOmF,UAGjB2Y,kBAGDxQ,QAAP,iBAAetL,EAAOyL,GACrBkP,GAAW,EACX5G,GAAUnE,QAAQ,SAAAiE,UAAWA,EAAQpI,EAAO,OAAS,WAAWzL,KAChE/D,GAAgBa,GAAM,QAASX,IAC/BF,GAAgBsC,GAAM,SAAUpC,IAChCysB,cAAc7O,GACd9d,GAAgBsC,GAAM,cAAekO,IACrCxQ,GAAgB2F,GAAO,aAAc6K,IACrCgD,GAAexT,GAAiBsC,GAAM,mCAAoC+N,IAC1EmD,GAAexT,GAAiBsC,GAAM,6BAA8BiO,IACpE2G,EAAa1H,OACb6B,GAAoBrR,QACf,IAAIyC,EAAI,EAAGA,EAAIpC,GAAWyC,OAAQL,GAAG,EACzCoR,GAAe7T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,IAC5DoR,GAAe7T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,mBAIvD0M,OAAP,qBACCtO,GAAO7B,OACPsD,GAAOkD,SACPhD,GAASF,GAAKoD,gBACdC,GAAQrD,GAAKmD,KACT1G,KACH2Z,GAAW3Z,GAAK8C,MAAMC,QACtB8b,GAAS7e,GAAK8C,MAAM+D,MACpBC,EAAW9G,GAAK+F,KAAKgB,SAAW0K,GAChC2N,GAAsBpf,GAAK+F,KAAK8nB,oBAAsBpc,GACtD2H,EAAqBtX,GAAKC,QAAQC,mBAAqB,OACvD8gB,EAAchhB,GAAK8G,YACnB5I,GAAK+F,KAAKC,QAAQ,gBAAiBF,eAC/Bc,IAAO,CACV+Y,GAAW,GACXrG,EAAY7S,SAASuP,cAAc,QACzBY,MAAMzE,OAAS,QACzBmH,EAAU1C,MAAM2D,SAAW,WAC3BlB,KApyCU,SAAbyU,oBAAmBnO,IAAY7S,sBAAsBghB,YAqyClDA,GACA5mB,EAAS4J,SAAS9Q,IAElB8F,cAAcqB,QAAUD,EAASC,QACjCkY,EAAanY,EAASC,SAAW,0BAA0B+V,KAAK5V,UAAUymB,WAC1E7V,EAA2C,IAArBhR,EAASC,QAC/BxG,GAAamB,GAAM,QAASX,IAC5BT,EAAQ,CAACoB,GAAMyB,GAAME,GAAQmD,IACzB5G,GAAKoH,YACRtB,cAAcsB,WAAa,SAAA0B,OAEzBX,EADG6lB,EAAKhuB,GAAKoH,iBAETe,KAAKW,EACTklB,EAAG7a,IAAIhL,EAAGW,EAAKX,WAET6lB,GAERhuB,GAAKgB,iBAAiB,iBAAkB,kBAAM4X,OAC9C5Y,GAAKgB,iBAAiB,mBAAoB,kBAAMsX,OAChDtY,GAAKgB,iBAAiB,aAAc,WACnCqX,GAAY,EAAG,GACfZ,EAAU,gBAEXzX,GAAKoH,WAAW,0BAA2B,kBAC1CsQ,KACOA,MAGRxU,QAAQC,KAAK,iCAEduU,KACA/W,GAAa4C,GAAM,SAAUpC,QAI5Bmb,EAAQ5Y,EAHLuqB,EAAYrnB,GAAMgQ,MACrBsX,EAASD,EAAUE,eACnBC,EAAiBpuB,GAAK+F,KAAKsoB,UAAUC,cAEtCF,EAAe1d,QAAU6d,OAAOC,eAAeJ,EAAgB,SAAU,CAAExsB,MAAO,wBAAoBgN,KAAKoN,MAAM,KAAM,MACvHiS,EAAUE,eAAiB,QAC3B7R,EAASlK,GAAWxL,IACpBjD,GAAUkZ,EAAI1a,KAAKC,MAAMka,EAAOK,IAAMhZ,GAAUL,OAAS,EACzD4E,GAAY2U,EAAI1a,KAAKC,MAAMka,EAAOI,KAAOxU,GAAY5E,OAAS,EAC9D4qB,EAAUD,EAAUE,eAAiBD,EAAUD,EAAUrR,eAAe,oBAExEmC,EAAgB0P,YAAYrX,GAAO,KACnCpX,GAAKyP,YAAY,GAAK,kBAAM5N,GAAW,IACvClB,GAAa4C,GAAM,cAAekO,IAClC9Q,GAAaiG,GAAO,aAAc6K,IAClCgD,GAAe9T,GAAc4C,GAAM,mCAAoC+N,IACvEmD,GAAe9T,GAAc4C,GAAM,6BAA8BiO,IACjEwN,EAAiBhf,GAAK8C,MAAM4rB,YAAY,aACxCtT,GAAYxX,KAAKob,GACjBxY,EAAehC,KACf2T,EAAenY,GAAKyP,YAAY,GAAK4I,IAAanJ,QAClDsD,EAAe,CAACjP,GAAM,mBAAoB,eACrCorB,EAAI7sB,GAAKoQ,WACZ0c,EAAI9sB,GAAKuM,YACN9K,GAAKsrB,QACR5P,EAAa0P,EACbzP,EAAc0P,GACJ3P,IAAe0P,GAAKzP,IAAgB0P,GAC9C/W,MAECtU,GAAM,mBAAoB8U,GAAavW,GAAM,OAAQuW,GAAavW,GAAM,SAAU+V,IACrFvF,GAAoB3R,IACpBoY,GAAUnE,QAAQ,SAAAiE,UAAWA,EAAQzI,OAAO,EAAG,KAC1C1M,EAAI,EAAGA,EAAIpC,GAAWyC,OAAQL,GAAG,EACrCoR,GAAe7T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,IAC5DoR,GAAe7T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,oBAMzDV,OAAP,gBAAc8F,sBACQA,IAAUyW,KAAoBzW,EAAKgmB,oBACpDC,EAAKjmB,EAAKkmB,aACdD,GAAMnB,cAAc7O,KAAoBA,EAAgBgQ,IAAON,YAAYrX,GAAO2X,0BACzDjmB,IAAUoP,EAAgD,IAA1BpS,cAAcqB,SAAiB2B,EAAKmmB,oBACzF,sBAAuBnmB,IAC1BwJ,GAAoBrR,KAAoBqR,GAAoB3R,GAAcmI,EAAKomB,mBAAqB,QACpGnX,GAAqE,KAApDjP,EAAKomB,kBAAoB,IAAI3uB,QAAQ,0BAIjD4uB,cAAP,uBAAqBnrB,EAAQ8E,OACxBpG,EAAID,EAAWuB,GAClBN,EAAIpC,GAAWf,QAAQmC,GACvBmM,EAAarO,GAAYkC,IACrBgB,GACJpC,GAAWqP,OAAOjN,EAAGmL,EAAa,EAAI,GAEnC/F,IACH+F,EAAavO,GAASqrB,QAAQ7pB,GAAMgH,EAAMlC,GAAOkC,EAAMrF,GAAQqF,GAAQxI,GAASqrB,QAAQjpB,EAAGoG,mBAItFsmB,gBAAP,yBAAuB3W,GACtBM,GAAUnE,QAAQ,SAAAlS,UAAKA,EAAEE,MAAQF,EAAEE,KAAK6V,QAAUA,GAAS/V,EAAEE,KAAK6N,MAAK,GAAM,oBAGvE4e,aAAP,sBAAoBjvB,EAASqe,EAAO0D,OAC/B7F,GAAUnL,GAAU/Q,GAAWqC,EAAWrC,GAAWA,GAASwgB,wBACjEpe,EAAS8Z,EAAO6F,EAAaxH,GAASE,IAAW4D,GAAS,SACpD0D,EAAqC,EAAxB7F,EAAO6E,MAAQ3e,GAAc8Z,EAAOI,KAAOla,EAASV,GAAKoQ,WAAsC,EAAzBoK,EAAO4E,OAAS1e,GAAc8Z,EAAOK,IAAMna,EAASV,GAAKuM,2BAG7IihB,mBAAP,4BAA0BlvB,EAASmvB,EAAgBpN,GAClDhR,GAAU/Q,KAAaA,EAAUqC,EAAWrC,QACxCkc,EAASlc,EAAQwgB,wBACpB1L,EAAOoH,EAAO6F,EAAaxH,GAASE,IACpCrY,EAA2B,MAAlB+sB,EAAyBra,EAAO,EAAMqa,KAAkBja,EAAaA,EAAUia,GAAkBra,GAAQqa,EAAehvB,QAAQ,KAAOmO,WAAW6gB,GAAkBra,EAAO,IAAMxG,WAAW6gB,IAAmB,SAClNpN,GAAc7F,EAAOI,KAAOla,GAAUV,GAAKoQ,YAAcoK,EAAOK,IAAMna,GAAUV,GAAKuM,2BAGtFmhB,QAAP,iBAAeC,MACd1W,GAAU/H,MAAM,GAAG4D,QAAQ,SAAAlS,SAAmB,mBAAdA,EAAEoG,KAAKyD,IAA2B7J,EAAE+N,UAC7C,IAAnBgf,EAAyB,KACxBC,EAAYjO,EAAW+N,SAAW,GACtC/N,EAAa,GACbiO,EAAU9a,QAAQ,SAAAnT,UAAKA,8CAn2BbqH,EAAM+J,GACjBrM,GAAgBV,cAAcgL,SAAS9Q,KAASkD,QAAQC,KAAK,6CAC7D2D,EAAS8H,WACJ/F,KAAKC,EAAM+J,MAs2BJjC,QAAU,YACV+e,WAAa,SAAAzG,UAAWA,EAAUvP,GAASuP,GAAStU,QAAQ,SAAA5Q,MACrEA,GAAUA,EAAO4S,MAAO,KACvBlT,EAAI8U,EAAajY,QAAQyD,GACxB,GAALN,GAAU8U,EAAa7H,OAAOjN,EAAG,GACjC8U,EAAa5U,KAAKI,EAAQA,EAAO4S,MAAMC,QAAS7S,EAAO0U,SAAW1U,EAAO4rB,aAAa,aAAc5vB,GAAK+F,KAAKoX,SAASnZ,GAAS8C,QAE7H0R,MACS9H,OAAS,SAAC4Z,EAAM/R,UAAUK,IAAY0R,EAAM/R,OAC5C1H,OAAS,SAAC/H,EAAM+J,UAAc,IAAI/M,GAAcgD,EAAM+J,OACtDoP,QAAU,SAAA4N,UAAQA,EAAOhY,MAAerR,GAAgBV,GAAcgL,aAAeuH,IAAY,OACjG/T,OAAS,SAAAC,WAAWjD,GAAWC,OAASgW,GAAqB,IAAVhT,EAAiB,EAAI,OACxEurB,kBAAoB7W,MACpB8W,UAAY,SAAC3vB,EAAS+hB,UAAe9P,GAAWjS,EAAS+hB,EAAaja,GAAcvE,QACpFqsB,cAAgB,SAAC5vB,EAAS+hB,UAAe/e,EAAeX,EAAWrC,GAAU+hB,EAAaja,GAAcvE,QACxGsN,QAAU,SAAA1E,UAAMiV,GAAKjV,OACrBwE,OAAS,kBAAMgI,GAAUxI,OAAO,SAAA7N,SAAmB,mBAAdA,EAAEoG,KAAKyD,SAC5C0jB,YAAc,mBAAQ5Y,OACtB6Y,gBAAkBnc,MAClB/S,iBAAmB,SAACJ,EAAM+T,OACnChP,EAAI8b,EAAW7gB,KAAU6gB,EAAW7gB,GAAQ,KAC/C+E,EAAEpF,QAAQoU,IAAahP,EAAE/B,KAAK+Q,OAElBzT,oBAAsB,SAACN,EAAM+T,OACtChP,EAAI8b,EAAW7gB,GAClB8C,EAAIiC,GAAKA,EAAEpF,QAAQoU,GACf,GAALjR,GAAUiC,EAAEgL,OAAOjN,EAAG,OAETysB,MAAQ,SAACjH,EAASpgB,GAKd,SAAhBsnB,GAAiBxvB,EAAM+T,OAClB0b,EAAW,GACdC,EAAW,GACXjI,EAAQroB,GAAKyP,YAAY8gB,EAAU,WAAO5b,EAAS0b,EAAUC,GAAWD,EAAW,GAAIC,EAAW,KAAMphB,eAClG,SAAAvM,GACN0tB,EAAStsB,QAAUskB,EAAMpa,SAAQ,GACjCoiB,EAASzsB,KAAKjB,EAAKkW,SACnByX,EAAS1sB,KAAKjB,GACd6tB,GAAYH,EAAStsB,QAAUskB,EAAMtV,SAAS,QAGhD5K,EAfG+K,EAAS,GACZud,EAAW,GACXF,EAAWznB,EAAKynB,UAAY,KAC5BC,EAAW1nB,EAAK0nB,UAAY,QAaxBroB,KAAKW,EACT2nB,EAAStoB,GAAyB,OAAnBA,EAAEiJ,OAAO,EAAG,IAAeqB,GAAY3J,EAAKX,KAAa,kBAANA,EAAyBioB,GAAcjoB,EAAGW,EAAKX,IAAMW,EAAKX,UAEzHsK,GAAY+d,KACfA,EAAWA,IACX7vB,GAAamF,GAAe,UAAW,kBAAM0qB,EAAW1nB,EAAK0nB,cAE9D7W,GAASuP,GAAStU,QAAQ,SAAA5Q,OACrBhB,EAAS,OACRmF,KAAKsoB,EACTztB,EAAOmF,GAAKsoB,EAAStoB,GAEtBnF,EAAO6V,QAAU7U,EACjBkP,EAAOtP,KAAKkC,GAAc+K,OAAO7N,MAE3BkQ,GAKmC,SAAvCwd,GAAwC3b,EAAY2I,EAASrB,EAAKtX,UAC1DA,EAAV2Y,EAAgB3I,EAAWhQ,GAAO2Y,EAAU,GAAK3I,EAAW,GAC/ChQ,EAANsX,GAAatX,EAAM2Y,IAAYrB,EAAMqB,GAAWrB,EAAM,EAAIqB,GAAWA,EAAUrB,GAAO,EAExE,SAAtBsU,GAAuB3sB,EAAQsQ,IACZ,IAAdA,EACHtQ,EAAO4S,MAAMgG,eAAe,gBAE5B5Y,EAAO4S,MAAMga,aAA4B,IAAdtc,EAAqB,OAASA,EAAY,OAASA,GAAapN,EAASC,QAAU,cAAgB,IAAM,OAErInD,IAAWP,IAAUktB,GAAoB/pB,GAAO0N,GAGjC,SAAhBuc,UAGqB7W,EAHHzQ,IAAAA,MAAOvF,IAAAA,OAAQgJ,IAAAA,KAC5B8jB,GAAQvnB,EAAM9D,eAAiB8D,EAAM9D,eAAe,GAAK8D,GAAOvF,OACnEzC,EAAQuvB,EAAK5W,OAASla,GAAK+F,KAAKoX,SAAS2T,GACzC9U,EAAOxX,SACHjD,EAAMwvB,YAAwC,IAA1B/U,EAAOza,EAAMwvB,WAAmB,MACjDD,GAAQA,IAASlqB,KAAWkqB,EAAKE,cAAgBF,EAAKG,cAAgBH,EAAKI,aAAeJ,EAAKtZ,cAAkB2Z,IAAWnX,EAAK1G,GAAkBwd,IAAOM,aAAcD,GAAUnX,EAAGqX,aAAcP,EAAOA,EAAK7V,WACtN1Z,EAAM+vB,UAAYR,GAAQA,IAAS9sB,IAAWxD,GAAYswB,KAAUK,IAAWnX,EAAK1G,GAAkBwd,IAAOM,YAAcD,GAAUnX,EAAGqX,YACxI9vB,EAAMwvB,WAAa/U,GAEhBza,EAAM+vB,WAAsB,MAATtkB,IACtBzD,EAAMgoB,kBACNhoB,EAAM/D,YAAa,GAIJ,SAAjBgsB,GAAkBxtB,EAAQpD,EAAM6wB,EAAQC,UAAWxqB,EAAS2J,OAAO,CAClE7M,OAAQA,EACRjD,SAAS,EACTmI,UAAU,EACViC,UAAU,EACVvK,KAAMA,EACNiK,QAAU6mB,EAASA,GAAUb,GAC7BlnB,QAAS+nB,EACThoB,OAAQgoB,EACR/jB,SAAU+jB,EACV5mB,SAAU,2BAAM2mB,GAAU9wB,GAAa4C,GAAM2D,EAASQ,WAAW,GAAIiqB,IAAgB,GAAO,IAC5F5mB,UAAW,4BAAM9J,GAAgBsC,GAAM2D,EAASQ,WAAW,GAAIiqB,IAAgB,MAWzD,SAAvBC,GAAuB9oB,GAoBH,SAAlB+oB,YAAwBC,GAAgB,EAGzB,SAAfC,KACCC,EAAO3f,GAAWrO,EAAQL,IAC1BsuB,EAAepT,GAAOQ,EAAa,EAAI,EAAG2S,GAC1CE,IAAqBC,EAAetT,GAAO,EAAGxM,GAAWrO,EAAQkE,MACjEkqB,EAAgBxQ,GAEK,SAAtByQ,KACC5I,EAAQvP,MAAMxN,EAAIgF,GAAOhD,WAAW+a,EAAQvP,MAAMxN,GAAKmB,EAAYrL,QAAU,KAC7EinB,EAAQ7S,MAAM0b,UAAY,mDAAqD5jB,WAAW+a,EAAQvP,MAAMxN,GAAK,UAC7GmB,EAAYrL,OAASqL,EAAYvL,QAAU,EAqBjC,SAAXiwB,KACCR,KACI7T,EAAMqK,YAAcrK,EAAMpV,KAAKiF,QAAUikB,IAC5CnkB,IAAgBmkB,EAAO9T,EAAMnL,SAAS,IAAMlF,EAAYmkB,GAAQ9T,EAAM4K,QAAQ,UAAWkJ,IAvD5Frf,GAAU7J,KAAUA,EAAO,IAC3BA,EAAKvD,eAAiBuD,EAAK4B,aAAe5B,EAAKoC,aAAc,EAC7DpC,EAAKlI,OAASkI,EAAKlI,KAAO,eAC1BkI,EAAKI,WAAaJ,EAAKI,SACvBJ,EAAKyD,GAAKzD,EAAKyD,IAAM,iBAEpB5J,EAAMqvB,EAWNI,EAAeN,EAkCf5T,EAAOsU,EAAcC,EAAczkB,EA9C/BkkB,EAA4DppB,EAA5DopB,iBAAkBQ,EAA0C5pB,EAA1C4pB,SAAUC,EAAgC7pB,EAAhC6pB,kBAAmB/oB,EAAad,EAAbc,UAEnD5F,EAASvB,EAAWqG,EAAK9E,SAAWP,GACpCmvB,EAAW5yB,GAAK+F,KAAKC,UAAU6sB,eAC/BC,EAAmBF,GAAYA,EAASG,MACxCtJ,EAAUpK,IAAgBvW,EAAK2gB,SAAWhnB,EAAWqG,EAAK2gB,UAAcqJ,IAAqC,IAAjBhqB,EAAK2gB,UAAsBqJ,EAAiB7uB,UAAY6uB,EAAiBrJ,WACrK5b,EAAczK,EAAeY,EAAQL,IACrCiK,EAAcxK,EAAeY,EAAQkE,IACrCuY,EAAQ,EACRuS,GAAgB9rB,EAASC,SAAWrF,GAAKmxB,eAAiBnxB,GAAKmxB,eAAexS,MAAQ3e,GAAKmxB,eAAehhB,MAAQnQ,GAAKoxB,YAAcpxB,GAAKoQ,WAC1IihB,EAAe,EACfC,EAA0B3gB,GAAYigB,GAAY,kBAAMA,EAAS/vB,IAAQ,kBAAM+vB,GAAY,KAE3FW,EAAgB7B,GAAextB,EAAQ8E,EAAKlI,MAAM,EAAM+xB,GAExDR,EAAe1gB,GACfwgB,EAAexgB,UAqChBgY,GAAWzpB,GAAK4d,IAAI6L,EAAS,CAAC/c,EAAG,QACjC5D,EAAK2B,YAAc,SAAAnF,UAAM+Z,GAAyB,cAAX/Z,EAAE1E,MA1B3B,SAAb0yB,gBACKxB,EAAe,CAClBhlB,sBAAsB+kB,QAClBrvB,EAASkP,GAAO/O,EAAKmJ,OAAS,GACjC0P,EAASyW,EAAapkB,EAAY3L,EAAIM,MACnCinB,GAAWjO,IAAW3N,EAAY3L,EAAI2L,EAAYrL,OAAQ,CAC7DqL,EAAYrL,OAASgZ,EAAS3N,EAAY3L,MACtCwK,EAAIgF,IAAQhD,WAAW+a,GAAWA,EAAQvP,MAAMxN,IAAM,GAAKmB,EAAYrL,QAC3EinB,EAAQ7S,MAAM0b,UAAY,mDAAqD5lB,EAAI,UACnF+c,EAAQvP,MAAMxN,EAAIA,EAAI,KACtBmB,EAAYvL,QAAUhB,GAAWC,MACjCgW,WAEM,EAER1J,EAAYrL,QAAU6vB,KACtBP,GAAgB,EAU+CwB,IAA2B,KAAR7S,GAA2B,eAAXnb,EAAE1E,MAA0B+B,EAAK8K,aAAgBnI,EAAEkI,SAA8B,EAAnBlI,EAAEkI,QAAQzJ,QAC5K+E,EAAKa,QAAU,WACdmoB,GAAgB,MACZyB,EAAY9S,EAChBA,EAAQ/O,IAAS5P,GAAKmxB,gBAAkBnxB,GAAKmxB,eAAexS,OAAU,GAAKuS,GAC3E9U,EAAMhP,QACNqkB,IAAc9S,GAASkQ,GAAoB3sB,EAAgB,KAARyc,IAAsByR,GAA2B,KACpGM,EAAe5kB,IACf6kB,EAAe5kB,IACfkkB,KACAK,EAAgBxQ,IAEjB9Y,EAAKc,UAAYd,EAAK6B,eAAiB,SAAChI,EAAM2M,MAC7CzB,EAAYrL,QAAU6vB,KACjB/iB,EAEE,CACNhO,GAAWC,YAGViyB,EAAe/K,EADZgL,EAAML,IAENlB,IAEHzJ,GADA+K,EAAgB5lB,KACmB,IAAN6lB,GAAc9wB,EAAK+wB,UAAa,KAC7DD,GAAO/C,GAAqC9iB,EAAa4lB,EAAe/K,EAAWpW,GAAWrO,EAAQkE,KACtGgW,EAAMpV,KAAKgF,QAAUqkB,EAAa1J,IAGnCA,GADA+K,EAAgB3lB,KACmB,IAAN4lB,GAAc9wB,EAAKgxB,UAAa,KAC7DF,GAAO/C,GAAqC7iB,EAAa2lB,EAAe/K,EAAWpW,GAAWrO,EAAQL,KACtGua,EAAMpV,KAAKiF,QAAUkkB,EAAaxJ,GAClCvK,EAAMsM,aAAa1W,SAAS2f,GAAKG,KAAK,MAClCvU,GAAcnB,EAAMpV,KAAKiF,SAAWikB,GAAyBA,EAAK,GAAtBwB,IAC/CxzB,GAAK4e,GAAG,GAAI,CAACF,SAAU6T,GAAUze,SAAU2f,SAlB5CzlB,EAAkBC,SAAQ,GAqB3BrE,GAAaA,EAAUjH,IAExBmG,EAAK+B,QAAU,WACdqT,EAAM2V,KAAO3V,EAAMhP,QACa,IAA5B1K,KAAa2uB,IAChBf,EAAgB,EAChBe,EAAe3uB,OAGjBsE,EAAKqB,SAAW,SAACxH,EAAMgJ,EAAIE,EAAIioB,EAAQC,MACtCnS,KAAewQ,GAAiBL,KAChCpmB,GAAMumB,GAAoBtkB,EAAYukB,EAAa2B,EAAO,KAAOnoB,EAAK6mB,GAAgB7vB,EAAK0K,OAAS1K,EAAK8J,GAAKmB,IAAgBjC,EAAKmoB,EAAO,KACtIjoB,EAAI,CACPgC,EAAYrL,QAAU6vB,SAClBlrB,EAAU4sB,EAAO,KAAOloB,EAC3Ba,EAAIvF,EAAUsrB,EAAe9vB,EAAK2K,OAAS3K,EAAK+J,EAAImB,IAAgBhC,EAAKkoB,EAAO,GAChFC,EAAW/B,EAAavlB,GACzBvF,GAAWuF,IAAMsnB,IAAavB,GAAgBuB,EAAWtnB,GACzDmB,EAAYmmB,IAEZnoB,GAAMF,IAAO4L,KAEfzO,EAAKgC,SAAW,WACf6lB,GAAoB3sB,GAAQkuB,GAA2B,KACvDpsB,GAAc9E,iBAAiB,UAAWuxB,IAC1C5xB,GAAamB,GAAM,SAAUywB,IACzB1kB,EAAY5J,SACf4J,EAAY7J,OAAO4S,MAAMoL,eAAiB,OAC1CnU,EAAY5J,OAAS2J,EAAY3J,QAAS,GAE3CovB,EAAcjjB,UAEftH,EAAKiC,UAAY,WAChB4lB,GAAoB3sB,GAAQ,GAC5B/C,GAAgBa,GAAM,SAAUywB,IAChCzsB,GAAc5E,oBAAoB,UAAWqxB,IAC7Cc,EAAc5iB,QAEf3H,EAAKqC,UAA6B,IAAlBrC,EAAKqC,WACrBxI,EAAO,IAAIuE,EAAS4B,IACfzG,IAAMgd,KACIxR,KAAiBA,EAAY,GAC5CwR,GAAcrf,GAAKi0B,OAAO9gB,IAAI1B,IAC9BzD,EAAoBrL,EAAKuN,IACzBgO,EAAQle,GAAK4e,GAAGjc,EAAM,CAAC8kB,KAAM,SAAUE,QAAQ,EAAMnJ,SAAS,EAAO1Q,QAASokB,EAAmB,QAAU,MAAOnkB,QAAS,QAASqQ,UAAW,CAACrQ,QAASqP,GAAqBvP,EAAaA,IAAe,kBAAMqQ,EAAMhP,WAAYwP,SAAUnH,EAAY4G,WAAYnQ,EAAkBlF,KAAKqV,aACpRxb,EA/LT,IA0CCuxB,GA9BA/C,GAAY,CAACgD,KAAM,EAAG3Y,OAAQ,GA6B9B4Y,GAAY,iCAEZzC,GAAiB,SAAjBA,eAAiBrsB,OACZ+uB,EAAUD,GAAUlX,KAAK5X,EAAEtB,OAAOswB,UAClCD,GAAWH,MACd5uB,EAAEE,YAAa,EACf0uB,GAAkBG,OAmJPjgB,KAAO,SAAAvT,UAAQkY,GAAU3E,KAAKvT,GAAS,SAAC8E,EAAG0O,UAAuC,KAAhC1O,EAAEmD,KAAKwe,iBAAmB,GAAY3hB,EAAEyW,OAAS/H,EAAE+H,OAAyC,KAAhC/H,EAAEvL,KAAKwe,iBAAmB,UACxIiN,QAAU,SAAAzrB,UAAQ,IAAI5B,EAAS4B,OAC/B0rB,gBAAkB,SAAA1rB,WACV,IAAVA,SACH1H,MAEK,IAAT0H,GAAiB1H,SACbA,EAAYgP,aAEP,IAATtH,SACH1H,GAAeA,EAAYqP,YAC3BrP,EAAc0H,OAGX2rB,EAAa3rB,aAAgB5B,EAAW4B,EAAO8oB,GAAqB9oB,UACxE1H,GAAeA,EAAY4C,SAAWywB,EAAWzwB,QAAU5C,EAAYqP,OACvEjQ,GAAYi0B,EAAWzwB,UAAY5C,EAAcqzB,GAC1CA,MAIM1uB,KAAO,CACpB5B,iBAAAA,EACAqtB,eAAAA,GACAlwB,WAAAA,GACAhB,SAAAA,GACA6F,OAAQ,CAEPuuB,GAAI,cACHrd,IAAmBI,EAAU,eAC7BJ,GAAkB7S,MAGnBmwB,IAAK,sBAAM7c,YAIC9X,GAAKE,eAAe4F"}