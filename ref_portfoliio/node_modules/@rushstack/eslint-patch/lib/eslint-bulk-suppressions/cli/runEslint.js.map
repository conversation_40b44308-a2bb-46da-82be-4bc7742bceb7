{"version": 3, "file": "runEslint.js", "sourceRoot": "", "sources": ["../../../src/eslint-bulk-suppressions/cli/runEslint.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;AAG3D,2DAAuD;AAEhD,KAAK,UAAU,cAAc,CAAC,KAAe,EAAE,IAA0B;IAC9E,MAAM,GAAG,GAAW,OAAO,CAAC,GAAG,EAAE,CAAC;IAClC,MAAM,UAAU,GAAW,IAAA,8BAAa,EAAC,GAAG,CAAC,CAAC;IAC9C,MAAM,EAAE,MAAM,EAAE,GAA4B,OAAO,CAAC,UAAU,CAAC,CAAC;IAChE,MAAM,MAAM,GAAW,IAAI,MAAM,CAAC;QAChC,WAAW,EAAE,IAAI;QACjB,GAAG;KACJ,CAAC,CAAC;IAEH,IAAI,OAA4B,CAAC;IACjC,IAAI,CAAC;QACH,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,wDAAa,4BAA4B,GAAC,CAAC;IACpE,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,UAAU,CAAC,CAAC,CAAC;YAChB,MAAM,KAAK,EAAE,CAAC;YACd,MAAM;QACR,CAAC;QAED,KAAK,OAAO,CAAC,CAAC,CAAC;YACb,MAAM,KAAK,EAAE,CAAC;YACd,MAAM;QACR,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAqB,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;QACxE,MAAM,gBAAgB,GAAW,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,GAAG,CACT,wGAAwG;QACtG,yBAAyB,GAAG,EAAE,CACjC,CAAC;AACJ,CAAC;AAvCD,wCAuCC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type { ESLint } from 'eslint';\nimport { getEslintPath } from './utils/get-eslint-cli';\n\nexport async function runEslintAsync(files: string[], mode: 'suppress' | 'prune'): Promise<void> {\n  const cwd: string = process.cwd();\n  const eslintPath: string = getEslintPath(cwd);\n  const { ESLint }: typeof import('eslint') = require(eslintPath);\n  const eslint: ESLint = new ESLint({\n    useEslintrc: true,\n    cwd\n  });\n\n  let results: ESLint.LintResult[];\n  try {\n    results = await eslint.lintFiles(files);\n  } catch (e) {\n    throw new Error(`@rushstack/eslint-bulk execution error: ${e.message}`);\n  }\n\n  const { write, prune } = await import('../bulk-suppressions-patch');\n  switch (mode) {\n    case 'suppress': {\n      await write();\n      break;\n    }\n\n    case 'prune': {\n      await prune();\n      break;\n    }\n  }\n\n  if (results.length > 0) {\n    const stylishFormatter: ESLint.Formatter = await eslint.loadFormatter();\n    const formattedResults: string = stylishFormatter.format(results);\n    console.log(formattedResults);\n  }\n\n  console.log(\n    '@rushstack/eslint-bulk: Successfully pruned unused suppressions in all .eslint-bulk-suppressions.json ' +\n      `files under directory ${cwd}`\n  );\n}\n"]}