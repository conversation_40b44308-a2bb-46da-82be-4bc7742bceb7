{"name": "trust", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-toast": "^1.2.14", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^27.0.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "motion": "^12.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.1", "web-vitals": "^2.1.0", "zod": "^3.25.74"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}